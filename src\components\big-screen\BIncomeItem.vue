<script setup lang="tsx">
import type { FunctionalComponent } from 'vue'

const props = withDefaults(defineProps<{
  icon: string
  title: string
  value: string | number
  valueColor?: 'primary' | 'error' | 'warning' | 'success'
  reverse?: boolean
}>(), {
  valueColor: 'primary',
  reverse: false,
})

const valueColorStyle = computed(() => {
  return { color: `var(--${props.valueColor}-color)` }
})

const Children: FunctionalComponent = () => {
  const components: [VNode, VNode] = [
    <n-p
      className="m-0 whitespace-nowrap"
      style="font-size: 10px"
    >
      {props.title}
    </n-p>,
    <n-el className="font-bold" style={valueColorStyle.value}>{props.value}</n-el>,
  ]

  return props.reverse ? components.reverse() : components
}
</script>

<template>
  <div class="flex items-center gap-1">
    <img width="46" height="62" :src="icon" alt="icon">
    <div>
      <Children />
    </div>
  </div>
</template>

<style scoped>

</style>

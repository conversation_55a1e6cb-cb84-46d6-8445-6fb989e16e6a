<script setup lang="tsx">
import { Normal } from '../components/Status'
import type { DataTableColumns } from 'naive-ui'

const columns: DataTableColumns<any> = [
  {
    title: '',
    key: 'index',
    render: (_v, index) => (
      <div class="flex-inline flex-col justify-center items-center">
        <img src="/src/assets/imgs/energy-storage/station-management/water-immersion-emergency-stop-number.png" alt="icon" />
        <n-text>
          {index + 1}
          {' '}
          #
        </n-text>
      </div>
    ),
  },
  {
    title: '水浸低',
    key: 'low',
    render: () => <Normal />,
  },
  {
    title: '水浸高',
    key: 'tall',
    render: () => <Normal />,
  },
  {
    title: '急停',
    key: 'emergencySTOP',
    render: () => <Normal />,
  },
  {
    title: () => '气溶胶反馈',
    key: 'aerosolFeedback',
    render: () => <Normal />,
  },
  {
    title: '交流断路器反馈',
    key: 'feedbackOfACCircuitBreaker',
    render: () => <Normal />,
  },
]
</script>

<template>
  <BCard title="水浸急停">
    <NDataTable striped :max-height="300" :columns="columns" :data="new Array(9).fill({})" />
  </BCard>
</template>

<style scoped>

</style>

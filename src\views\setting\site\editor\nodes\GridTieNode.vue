<script setup lang="ts">
import ImageNode from '@/views/setting/site/editor/nodes/ImageNode.vue'
import CabinetRow from '@/assets/imgs/setting/asset-management/cabinet-row.png'
import NodeWrapper from '@/views/setting/site/editor/nodes/NodeWrapper.vue'
import type { NodeProps } from '@vue-flow/core'

defineProps<NodeProps>()
</script>

<template>
  <NodeWrapper v-bind="$props">
    <ImageNode :image="CabinetRow" :meta="data.meta" />
  </NodeWrapper>
</template>

<style scoped>

</style>

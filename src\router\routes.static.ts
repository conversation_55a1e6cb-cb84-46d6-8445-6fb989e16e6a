export const staticRoutes: AppRoute.RowRoute[] = [
  {
    name: 'dashboard',
    path: '/dashboard',
    title: '仪表盘',
    requiresAuth: true,
    icon: 'icon-park-outline:analysis',
    menuType: 'dir',
    componentPath: null,
    id: 1,
    pid: null,
  },
  {
    name: 'workbench',
    path: '/dashboard/workbench',
    title: '工作台',
    requiresAuth: true,
    icon: 'icon-park-outline:alarm',
    pinTab: true,
    menuType: 'page',
    componentPath: '/dashboard/workbench/index.vue',
    id: 2,
    pid: 1,
  },
  {
    name: 'integrated-energy',
    path: '/integrated-energy',
    title: '综能',
    requiresAuth: true,
    icon: 'carbon:cloud-service-management',
    menuType: 'dir',
    componentPath: null,
    id: 2,
    pid: null,
  },
  {
    name: 'photovoltaics',
    path: '/photovoltaics',
    title: '光伏',
    requiresAuth: true,
    icon: 'icon-park-outline:three-hexagons',
    menuType: 'dir',
    componentPath: null,
    id: 3,
    pid: null,
  },
  {
    name: 'energy-storage',
    path: '/energy-storage',
    title: '储能',
    requiresAuth: true,
    icon: 'carbon:storage-pool',
    menuType: 'dir',
    componentPath: null,
    id: 4,
    pid: null,
  },
  {
    name: 'energy-storage-map',
    path: '/energy-storage/map',
    title: '储能地图',
    requiresAuth: true,
    icon: 'carbon:map',
    menuType: 'page',
    componentPath: '/energy-storage/map/index.vue',
    id: 401,
    pid: 4,
  },
  {
    name: 'power-station-management',
    path: '/energy-storage/power-station-management',
    title: '电站管理',
    requiresAuth: true,
    icon: 'icon-park-outline:database-power',
    menuType: 'dir',
    id: 402,
    pid: 4,
  },
  {
    name: 'main-monitoring',
    path: '/energy-storage/station-management/main-monitoring',
    title: '主体监控',
    requiresAuth: true,
    icon: 'icon-park-outline:monitor-one',
    menuType: 'page',
    componentPath: '/energy-storage/station-management/main-monitoring/index.vue',
    id: 40201,
    pid: 402,
  },
  {
    name: 'battery-monitoring',
    path: '/energy-storage/station-management/battery-monitoring',
    title: '电池监控',
    requiresAuth: true,
    icon: 'icon-park-outline:car-battery',
    menuType: 'page',
    componentPath: '/energy-storage/station-management/battery-monitoring/index.vue',
    id: 40202,
    pid: 402,
  },
  {
    name: 'pcs-monitoring',
    path: '/energy-storage/station-management/pcs-monitoring',
    title: 'PCS监控',
    requiresAuth: true,
    icon: 'icon-park-outline:storage-card-one',
    menuType: 'page',
    componentPath: '/energy-storage/station-management/pcs-monitoring/index.vue',
    id: 40203,
    pid: 402,
  },
  {
    name: 'security-monitoring',
    path: '/energy-storage/station-management/security-monitoring',
    title: '安全监控',
    requiresAuth: true,
    icon: 'icon-park-outline:flight-safety',
    menuType: 'page',
    componentPath: '/energy-storage/station-management/security-monitoring/index.vue',
    id: 40204,
    pid: 402,
  },
  {
    name: 'energy-storage-finance',
    path: '/energy-storage/finance',
    title: '财务管理',
    requiresAuth: true,
    icon: 'icon-park-outline:finance',
    menuType: 'dir',
    componentPath: null,
    id: 403,
    pid: 4,
  },
  {
    name: 'consumption-analysis',
    path: '/energy-storage/finance/consumption',
    title: '能耗分析',
    requiresAuth: true,
    icon: 'icon-park-outline:analysis',
    menuType: 'page',
    componentPath: '/energy-storage/finance/consumption/index.vue',
    id: 40301,
    pid: 403,
  },
  {
    name: 'revenue-analysis',
    path: '/energy-storage/finance/revenue',
    title: '收益分析',
    requiresAuth: true,
    icon: 'icon-park-outline:trend-charts',
    menuType: 'page',
    componentPath: '/energy-storage/finance/revenue/index.vue',
    id: 40302,
    pid: 403,
  },
  {
    name: 'charging-piles',
    path: '/charging-piles',
    title: '充电桩',
    requiresAuth: true,
    icon: 'icon-park-outline:battery-charge',
    menuType: 'dir',
    componentPath: null,
    id: 5,
    pid: null,
  },
  {
    name: 'operation-log',
    path: '/operation-log',
    title: '操作日志',
    requiresAuth: true,
    icon: 'carbon:ibm-knowledge-catalog-standard',
    menuType: 'dir',
    componentPath: null,
    id: 6,
    pid: null,
  },
  {
    name: 'commonList',
    path: '/operation-log/commonList',
    title: '常用列表',
    requiresAuth: true,
    icon: 'icon-park-outline:every-user',
    componentPath: '/demo/list/commonList/index.vue',
    id: 601,
    pid: 6,
  },
  {
    name: 'setting',
    path: '/setting',
    title: '系统设置',
    requiresAuth: true,
    icon: 'icon-park-outline:setting',
    menuType: 'dir',
    componentPath: '/setting/index.vue',
    id: 7,
    pid: null,
  },
  {
    name: 'assetManagement',
    path: '/setting/assetManagement',
    title: '资产管理',
    requiresAuth: true,
    icon: 'icon-park-outline:setting',
    menuType: 'dir',
    componentPath: null,
    id: 704,
    pid: 7,
  },
  {
    name: 'stationAssets',
    path: '/setting/assetManagement/stationAssets',
    title: '站场资产',
    requiresAuth: true,
    icon: 'icon-park-outline:every-user',
    menuType: 'page',
    componentPath: '/setting/asset-management/station-assets/index.vue',
    id: 70401,
    pid: 704,
  },
  {
    name: 'equipmentManagement',
    path: '/setting/device',
    title: '设备管理',
    requiresAuth: true,
    icon: 'carbon:kiosk-device',
    menuType: 'page',
    componentPath: '/setting/device/index.vue',
    id: 701,
    pid: 7,
  },
  {
    name: 'accountSetting',
    path: '/setting/account',
    title: '用户设置',
    requiresAuth: true,
    icon: 'icon-park-outline:every-user',
    componentPath: '/setting/account/index.vue',
    id: 701,
    pid: 7,
  },
  {
    name: 'dictionarySetting',
    path: '/setting/dictionary',
    title: '字典设置',
    requiresAuth: true,
    icon: 'icon-park-outline:book-one',
    componentPath: '/setting/dictionary/index.vue',
    id: 702,
    pid: 7,
  },

  {
    name: 'menuSetting',
    path: '/setting/menu',
    title: '菜单设置',
    requiresAuth: true,
    icon: 'icon-park-outline:application-menu',
    componentPath: '/setting/menu/index.vue',
    id: 703,
    pid: 7,
  },
  {
    name: '运维中心',
    path: '/operationsCenter',
    title: '运维中心',
    requiresAuth: true,
    icon: 'icon-park-outline:application-menu',
    menuType: 'dir',
    componentPath: null,
    id: 8,
    pid: null,
  },
  {
    name: 'userCenter',
    path: '/userCenter',
    title: '个人中心',
    requiresAuth: true,
    icon: 'carbon:user-avatar-filled-alt',
    componentPath: '/user-center/index.vue',
    id: 999,
    pid: null,
    hide: true,
  },
]

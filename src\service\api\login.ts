import { request } from '../http'

/** 登录请求参数 */
export interface LoginParams {
  username: string
  password: string
  code: string
  uuid: string
}

/** 登录响应结果 */
interface LoginResponse extends Api.BaseResponse {
  /** JWT访问令牌 */
  token: string
}

/** 验证码响应结果 */
interface CaptchaResponse extends Api.BaseResponse {
  /** 验证码图片base64数据 */
  img: string
  /** 验证码唯一标识 */
  uuid: string
  /** 是否开启验证码 */
  captchaEnabled: boolean
}

/** 用户信息响应结果 */
interface UserInfoResponse extends Api.BaseResponse {
  /** 用户角色 */
  roles: string[]
  /** 用户权限 */
  permissions: string[]
  /** 用户信息 */
  user: Entity.UserInfo
}

export function login(data: LoginParams) {
  const methodInstance = request.Post<LoginResponse>('/login', data)
  methodInstance.meta = {
    authRole: null,
  }
  return methodInstance
}

export function logout() {
  return request.Post<Api.BaseResponse>('/logout')
}

export function getCodeImg() {
  return request.Get<CaptchaResponse>('/captchaImage')
}

// 获取用户详细信息
export function getInfo() {
  return request.Get<UserInfoResponse>('/getInfo')
}

// 获取用户菜单列表
export function getMenus() {
  return request.Get<Api.ResponseWithData<Entity.Menu[]>>('/getMenus')
}

export function fetchUpdateToken(data: any) {
  const method = request.Post<Entity.UserInfo>('/updateToken', data)
  method.meta = {
    authRole: 'refreshToken',
  }
  return method
}

<script setup lang="tsx">
import { useEcharts } from '@/hooks'
import { useStatusOptions } from '../echarts-options/useStatusOptions'
import type { FunctionalComponent } from 'vue'
import ChargingStatus from '@/assets/imgs/energy-storage/station-management/charging-status.png'
import FaultStatus from '@/assets/imgs/energy-storage/station-management/fault-status.png'
import Alertable from '@/assets/imgs/energy-storage/station-management/alertable.png'

const options = useStatusOptions()
useEcharts('echarts', options)

function SystemState() {
  return (
    <div>
      <n-h3 class="text-center">
        <n-text>系统状态</n-text>
      </n-h3>
      <div class="flex justify-center gap-3">
        <n-text>离网</n-text>
        <n-text type="info">并网</n-text>
        <n-text>降频</n-text>
      </div>
    </div>
  )
}

function RunningState() {
  return (
    <div>
      <n-h3 class="text-center">
        <n-text>运行状态</n-text>
      </n-h3>
      <div class="flex justify-center gap-3">
        <n-text>停机</n-text>
        <n-text type="info">待机</n-text>
        <n-text>运行</n-text>
      </div>
    </div>
  )
}

const Item: FunctionalComponent<{ icon: string, name: string, status: string, text: [string, string] }> = props => (
  <div class="flex items-center justify-between">
    <img class="w-[36px] h-[36px]" src={props.icon} alt="icon" />
    <n-text class="basis-[6em]">{props.name}</n-text>
    <n-text type="info" class="basis-[2em]">{props.status}</n-text>
    <n-text class="basis-[2em]">{props.text.at(0)}</n-text>
    <n-text class="basis-[2em]">{props.text.at(1)}</n-text>
  </div>
)
</script>

<template>
  <BCard title="实时状态">
    <template #extend>
      <NSelect class="w-[120px]" size="small" placeholder="场站名称" />
      <NSelect class="w-[120px]" size="small" placeholder="1#PCS" />
    </template>
    <header class="flex items-center justify-center gap-2 mx-8 h-[150px] status-bg">
      <SystemState />
      <div ref="echarts" class="w-[150px] h-[150px]" />
      <RunningState />
    </header>
    <main class="px-8 flex flex-col justify-around h-[calc(100%-150px)]">
      <Item :icon="ChargingStatus" name="充放电状态" status="充电" :text="['静止', '放电']" />
      <Item :icon="FaultStatus" name="故障状态" status="正常" :text="['', '放电']" />
      <Item :icon="Alertable" name="告警状态" status="正常" :text="['', '放电']" />
    </main>
  </BCard>
</template>

<style scoped>
.status-bg {
  --border-bg: #2090FF;
  --start-bg: #CDEAFF;

  background-image: linear-gradient(to right,
  var(--border-bg) 3px,
  var(--start-bg) 3px,
  transparent 50%,
  var(--start-bg) calc(100% - 3px),
  var(--border-bg) calc(100% - 3px));
  background-size: 100% 70%;
  background-repeat: no-repeat;
  background-position: center center;
}

/**兼容暗黑模式**/
.dark .status-bg {
  --border-bg: #2090FF;
  --start-bg: #234c75;
}
</style>

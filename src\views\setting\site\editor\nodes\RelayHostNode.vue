<script setup lang="ts">
import type { NodeProps } from '@vue-flow/core'
import NodeWrapper from '@/views/setting/site/editor/nodes/NodeWrapper.vue'
import Discharge from '/src/assets/imgs/energy-storage/station-management/discharge-today.png'
import ImageNode from '@/views/setting/site/editor/nodes/ImageNode.vue'

defineProps<NodeProps>()
</script>

<template>
  <NodeWrapper v-bind="$props">
    <ImageNode :image="Discharge" :meta="data.meta" />
  </NodeWrapper>
</template>

<style scoped>

</style>

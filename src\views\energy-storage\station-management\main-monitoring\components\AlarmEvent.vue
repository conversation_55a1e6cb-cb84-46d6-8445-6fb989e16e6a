<script setup lang="tsx">
import BCard from '@/components/big-screen/BCard.vue'

import Fault from '@/assets/imgs/energy-storage/station-management/Fault.png'
import Completed from '@/assets/imgs/energy-storage/station-management/Completed.png'
import RepairInProgress from '@/assets/imgs/energy-storage/station-management/repair-in-progress.png'
import type { TextProps } from 'naive-ui'

type Level = TextProps['type']

const data = Array.from({ length: 6 }).map((_v, i) => ({
  index: i + 1,
  type: '电箱故障',
  position: '蜀山区肥西路和望江西路交口',
  level: 'success' as Level,
}))

function formatLevel(level: Level) {
  return <n-text type={level}>已完成</n-text>
}
</script>

<template>
  <BCard title="告警事件">
    <header class="flex justify-between items-center px-3 mb-2">
      <BIncomeItem title="设备故障数" value-color="error" :icon="Fault" value="xxx" />
      <BIncomeItem title="抢修中" value-color="warning" :icon="RepairInProgress" value="xxx" />
      <BIncomeItem title="已完成" value-color="success" :icon="Completed" value="xxx" />
    </header>
    <n-scrollbar style="max-height: 160px">
      <n-table size="small" style="--n-td-color: transparent" striped>
        <tbody>
          <tr v-for="item in data" :key="item.index">
            <td>{{ item.index }}</td>
            <td>{{ item.type }}</td>
            <td>{{ item.position }}</td>
            <td>
              <component :is="formatLevel(item.level)" />
            </td>
          </tr>
        </tbody>
      </n-table>
    </n-scrollbar>
  </BCard>
</template>

<style scoped>

</style>

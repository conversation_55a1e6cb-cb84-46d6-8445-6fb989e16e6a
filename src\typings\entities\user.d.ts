/// <reference path="../global.d.ts"/>

/** 系统用户相关实体定义 */
namespace Entity {
  /** 用户数据库表字段 */
  interface UserInfo {
    /** 创建者 */
    createBy: string
    /** 创建时间 */
    createTime: string
    /** 更新者 */
    updateBy: any
    /** 更新时间 */
    updateTime: any
    /** 备注 */
    remark: string
    /** 数据范围 */
    dataScope: any
    /** 用户ID */
    userId: number
    /** 部门ID */
    deptId: number
    /** 用户账号 */
    userName: string
    /** 用户昵称 */
    nickName: string
    /** 用户邮箱 */
    email: string
    /** 手机号码 */
    phoneNumber: string
    /** 用户性别（0男 1女 2未知） */
    sex: '0' | '1'
    /** 头像地址 */
    avatar: string
    /** 密码 */
    password: string
    /** 帐号状态（0正常 1停用） */
    status: '0' | '1'
    /** 删除标志（0代表存在 2代表删除） */
    delFlag: string
    /** 最后登录IP */
    loginIp: string
    /** 最后登录时间 */
    loginDate: string
    /** 部门对象 */
    dept: Entity.Dept
    /** 角色对象 */
    roles: string[]
    /** 角色组 */
    roleIds: any
    /** 岗位组 */
    postIds: any
    /** 角色ID */
    roleId: any
    /** 单用户标识 */
    singleUser: any
    /** 管理员标识 */
    admin: boolean
  }
}

# 卡号管理

## 功能概述

卡号管理模块提供了对系统中卡号信息的完整管理功能，包括卡号的增删改查操作。

## 主要功能

### 1. 卡号列表查询
- 支持分页查询卡号列表
- 支持按所属站点筛选
- 支持按卡号查询

### 2. 卡号信息管理
- **新增卡号**：添加新的卡号信息
- **编辑卡号**：修改现有卡号信息
- **删除卡号**：删除卡号记录
- **批量删除**：支持多选批量删除

### 3. 卡号字段说明
- `id`: 主键
- `cardNumber`: 卡号
- `accountOpeningTime`: 开户时间
- `serviceStartTime`: 服务开始时间
- `serviceStopTime`: 服务结束时间
- `periodicPackageData`: 周期套餐流量
- `usedData`: 已使用流量
- `siteId`: 站点ID

### 4. 搜索功能
- **所属站点**：下拉选择器，支持按站点筛选
- **卡号查询**：输入框，支持按卡号模糊查询

## API接口

### 查询卡号列表
```
GET /base/card-number/list
```

### 查询卡号详情
```
GET /base/card-number/{id}
```

### 新增卡号
```
POST /base/card-number
```

### 修改卡号
```
PUT /base/card-number
```

### 删除卡号
```
DELETE /base/card-number/{ids}
```

### 导出卡号
```
POST /base/card-number/export
```

## 文件结构

```
card-number-management/
├── index.vue                 # 主页面
├── columns/                  # 列配置目录
│   ├── tableColumns.ts      # 表格列配置
│   └── searchFormColumn.ts  # 搜索表单列配置
├── types/                   # 类型定义目录
│   └── CardNumber.ts        # 卡号类型定义
└── README.md               # 说明文档
```

## 技术特点

- 使用 ProNaiveUI 组件库
- 支持表格多选和批量操作
- 响应式设计，适配不同屏幕尺寸
- 完整的错误处理和用户提示
- TypeScript 类型安全

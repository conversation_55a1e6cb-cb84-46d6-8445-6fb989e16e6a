<script setup lang="tsx">
import type { FunctionalComponent } from 'vue'

const Item: FunctionalComponent<{ label: string, value: string }> = props => (
  <div class="flex flex-col items-center">
    <n-text>{props.label}</n-text>
    <n-text depth="1" strong>{props.value}</n-text>
  </div>
)
</script>

<template>
  <BCard title="直流电运行信息">
    <main class="h-[320px] grid grid-rows-3 grid-cols-[8em_1fr_8em] justify-center items-center p-8">
      <Item label="直流电压" value="XX V" />
      <img
        class="row-span-3 w-[311px] h-[189px] justify-self-center"
        src="@/assets/imgs/energy-storage/station-management/dc-operation-information.png"
        alt="icon"
      >
      <Item label="环境温度" value="XX °C" />
      <Item label="直流电流" value="XX A" />
      <Item class="col-start-1" label="直流功率" value="XX KW" />
      <Item label="模块温度" value="XX °C" />
    </main>
  </BCard>
</template>

<style scoped>

</style>

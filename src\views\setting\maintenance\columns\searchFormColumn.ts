import type { ProSearchFormColumns } from 'pro-naive-ui'

export function useSearchFormColumn() {
  return computed<ProSearchFormColumns>(() => {
    return [
      {
        title: '设备名称',
        path: 'deviceName',
        field: 'input',
        placeholder: '请输入设备名称',
      },
      {
        title: '生产厂家',
        path: 'producer',
        field: 'input',
        placeholder: '请输入生产厂家',
      },
      {
        title: '设备型号',
        path: 'deviceModel',
        field: 'input',
        placeholder: '请输入设备型号',
      },
    ]
  })
}

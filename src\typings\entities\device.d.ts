/// <reference path="../global.d.ts"/>

/** 设备数据库表字段 */
namespace Entity {
  interface Device {
    deviceId: string
    deviceType: string
    deviceName: string
    deviceModel: string
    deviceUnit: string
    deviceNum: string
    maintenanceCycle: string
    maintenanceInfo: string
    specification: string
    producer: string
    remark: string
    createBy: string
    createTime: string
    updateBy: string
    updateTime: string
    delFlag: string
    dataScope: string
    // 为了兼容性，添加 id 字段（与 deviceId 相同）
    id: string
    // 设备价格字段
    price: number
    type: '1' | '2' // 类型：1=拓扑图生成；2=手动添加
  }
}

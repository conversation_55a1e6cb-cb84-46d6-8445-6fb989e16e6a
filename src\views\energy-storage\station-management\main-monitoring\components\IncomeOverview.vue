<script setup lang="ts">
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import BIncomeItem from '@/components/big-screen/BIncomeItem.vue'
import { useIncomeOverviewOptions } from '../echarts-options/IncomeOverviewOptions'

import Yesterday from '@/assets/imgs/energy-storage/map/yesterday.png'
import Moon from '@/assets/imgs/energy-storage/map/moon.png'
import ChargingToday from '@/assets/imgs/energy-storage/station-management/charging-today.png'
import DischargeToday from '@/assets/imgs/energy-storage/station-management/discharge-today.png'

const echartsOptions = useIncomeOverviewOptions()

useEcharts('echarts', echartsOptions)
</script>

<template>
  <BCard title="收益概况">
    <template #extend>
      <n-select class="w-[120px]" size="small" placeholder="选择站场" />
    </template>
    <div ref="echarts" class="h-[180px] w-full" />

    <div
      class="grid grid-cols-[120px_120px] grid-rows-2 gap-row-4 gap-col-10 justify-center justify-items-center items-center mt-5"
    >
      <BIncomeItem title="昨日收益(元）" :icon="Yesterday" value="xxx" />
      <BIncomeItem title="月收益(万元）" :icon="Moon" value="xxx" />
      <BIncomeItem title="今日充电量(MWh）" :icon="ChargingToday" value="xxx" />
      <BIncomeItem title="今日放电量(MWh）" :icon="DischargeToday" value="xxx" />
    </div>
  </BCard>
</template>

<style scoped>

</style>

import type { DataTableColumns } from 'naive-ui'
import { NButton, NSpace, NTag } from 'naive-ui'
import type { ProSearchFormColumns } from 'pro-naive-ui'

// 搜索表单数据类型
export interface SearchFormData {
  deptName?: string
  status?: '0' | '1'
}

// 搜索表单列配置
export const searchColumns: ProSearchFormColumns<SearchFormData> = [
  {
    title: '部门名称',
    path: 'deptName',
    field: 'input',
    fieldProps: {
      clearable: true,
    },
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      clearable: true,
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
  },
]

// 表格列配置
export function createTableColumns(options: {
  onEdit: (row: Entity.Dept) => void
  onDelete: (deptId: number) => void
}): DataTableColumns<Entity.Dept> {
  const { onEdit, onDelete } = options

  return [
    {
      title: '部门名称',
      key: 'deptName',
      width: 300,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '排序',
      key: 'orderNum',
      width: 80,
      align: 'center',
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: (row) => {
        return (
          <NTag type={row.status === '0' ? 'success' : 'error'} bordered={false}>
            {row.status === '0' ? '正常' : '停用'}
          </NTag>
        )
      },
    },
    {
      title: '负责人',
      key: 'leader',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '联系电话',
      align: 'center',
      key: 'phone',
    },
    {
      title: '邮箱',
      align: 'center',
      key: 'email',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '创建时间',
      align: 'center',
      key: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      align: 'center',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (row) => {
        return (
          <NSpace justify="center">
            <NButton text onClick={() => onEdit(row)}>编辑</NButton>
            <NButton text type="error" onClick={() => onDelete(row.deptId)}>删除</NButton>
          </NSpace>
        )
      },
    },
  ]
}

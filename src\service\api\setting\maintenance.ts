import { request } from '../../http'

// 设备维护查询参数类型
export interface MaintenanceQueryParams {
  id?: number
  siteId?: string
  maintenanceType?: string
  deviceModel?: string
  deviceType?: string
  maintenanceTime?: string
  pageNum?: number
  pageSize?: number
}

// 查询设备维护列表
export function listMaintenance(params?: MaintenanceQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.Maintenance[]>>('/base/maintenance/list', { params })
}

// 查询设备维护详细
export function getMaintenance(id: number) {
  return request.Get<Api.ResponseWithData<Entity.Maintenance>>(`/base/maintenance/${id}`)
}

// 新增设备维护
export function addMaintenance(data: Omit<Entity.Maintenance, 'id'>) {
  return request.Post<Api.BaseResponse>('/base/maintenance', data)
}

// 修改设备维护
export function updateMaintenance(data: Entity.Maintenance) {
  return request.Put<Api.BaseResponse>('/base/maintenance', data)
}

// 删除设备维护
export function delMaintenance(ids: string) {
  return request.Delete<Api.BaseResponse>(`/base/maintenance/${ids}`)
}

// 导出设备维护
export function exportMaintenance(params?: MaintenanceQueryParams) {
  return request.Post('/base/maintenance/export', { params })
}

/// <reference path="../global.d.ts"/>

/** 登录日志数据库表字段 */
namespace Entity {
  interface LoginLog {
    /** 访问ID */
    infoId: number
    /** 用户账号 */
    userName: string
    /** 登录IP地址 */
    ipaddr: string
    /** 登录地点 */
    loginLocation?: string
    /** 浏览器类型 */
    browser?: string
    /** 操作系统 */
    os?: string
    /** 登录状态（0成功 1失败） */
    status: '0' | '1'
    /** 提示消息 */
    msg?: string
    /** 数据范围 */
    dataScope: any
    /** 访问时间 */
    loginTime: string
    /** 创建者 */
    createBy?: string
    /** 创建时间 */
    createTime?: string
    /** 更新者 */
    updateBy?: string
    /** 更新时间 */
    updateTime?: string
    /** 备注 */
    remark?: string
  }
}

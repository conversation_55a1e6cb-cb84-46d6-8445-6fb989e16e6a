<script setup lang="ts">
import { computed, ref } from 'vue'
import { NDatePicker, NRadioButton, NRadioGroup, useThemeVars } from 'naive-ui'

import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import type { ECOption } from '@/hooks/useEcharts'

const activeTab = ref('day')
const startTime = ref<number | null>(Date.now() - 24 * 60 * 60 * 1000)
const themeVars = useThemeVars()

const chartOptions = computed<ECOption>(() => {
  // 根据选中的标签生成不同的数据
  let times, peakCharge, shoulderCharge, flatCharge, valleyCharge, deepValleyCharge
  let peakFee, shoulderFee, flatFee, valleyFee, deepValleyFee

  switch (activeTab.value) {
    case 'day':
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      peakCharge = [18, 15, 22, 25, 20, 23, 21]
      shoulderCharge = [14, 12, 18, 21, 17, 19, 18]
      flatCharge = [10, 8, 12, 15, 11, 13, 12]
      valleyCharge = [6, 5, 8, 10, 7, 9, 8]
      deepValley<PERSON>harge = [4, 3, 5, 7, 4, 6, 5]
      peakFee = [5.4, 4.5, 6.6, 7.5, 6.0, 6.9, 6.3]
      shoulderFee = [4.2, 3.6, 5.4, 6.3, 5.1, 5.7, 5.4]
      flatFee = [3.0, 2.4, 3.6, 4.5, 3.3, 3.9, 3.6]
      valleyFee = [1.8, 1.5, 2.4, 3.0, 2.1, 2.7, 2.4]
      deepValleyFee = [1.2, 0.9, 1.5, 2.1, 1.2, 1.8, 1.5]
      break
    case 'month':
      times = ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
      peakCharge = [540, 570, 510, 600, 570, 560, 550]
      shoulderCharge = [420, 450, 400, 480, 450, 440, 430]
      flatCharge = [300, 320, 280, 360, 330, 320, 310]
      valleyCharge = [180, 190, 170, 220, 200, 190, 180]
      deepValleyCharge = [120, 130, 110, 150, 140, 130, 120]
      peakFee = [162, 171, 153, 180, 171, 168, 165]
      shoulderFee = [126, 135, 120, 144, 135, 132, 129]
      flatFee = [90, 96, 84, 108, 99, 96, 93]
      valleyFee = [54, 57, 51, 66, 60, 57, 54]
      deepValleyFee = [36, 39, 33, 45, 42, 39, 36]
      break
    case 'year':
      times = ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
      peakCharge = [16200, 15400, 17000, 15800, 16600, 16300, 16000]
      shoulderCharge = [12600, 12000, 13200, 12200, 12800, 12600, 12400]
      flatCharge = [9000, 8600, 9400, 8800, 9200, 9000, 8800]
      valleyCharge = [5400, 5100, 5700, 5300, 5600, 5400, 5300]
      deepValleyCharge = [3600, 3400, 3800, 3600, 3800, 3600, 3500]
      peakFee = [4860, 4620, 5100, 4740, 4980, 4890, 4800]
      shoulderFee = [3780, 3600, 3960, 3660, 3840, 3780, 3720]
      flatFee = [2700, 2580, 2820, 2640, 2760, 2700, 2640]
      valleyFee = [1620, 1530, 1710, 1590, 1680, 1620, 1590]
      deepValleyFee = [1080, 1020, 1140, 1080, 1140, 1080, 1050]
      break
    default:
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      peakCharge = [18, 15, 22, 25, 20, 23, 21]
      shoulderCharge = [14, 12, 18, 21, 17, 19, 18]
      flatCharge = [10, 8, 12, 15, 11, 13, 12]
      valleyCharge = [6, 5, 8, 10, 7, 9, 8]
      deepValleyCharge = [4, 3, 5, 7, 4, 6, 5]
      peakFee = [5.4, 4.5, 6.6, 7.5, 6.0, 6.9, 6.3]
      shoulderFee = [4.2, 3.6, 5.4, 6.3, 5.1, 5.7, 5.4]
      flatFee = [3.0, 2.4, 3.6, 4.5, 3.3, 3.9, 3.6]
      valleyFee = [1.8, 1.5, 2.4, 3.0, 2.1, 2.7, 2.4]
      deepValleyFee = [1.2, 0.9, 1.5, 2.1, 1.2, 1.8, 1.5]
  }

  // 构建系列数据
  const series: any[] = [
    // 堆叠柱状图 - 充电量
    {
      name: '尖充电量',
      type: 'bar',
      stack: 'charge',
      barWidth: 15,
      label: {
        show: false,
      },
      data: peakCharge,
    },
    {
      name: '峰充电量',
      type: 'bar',
      stack: 'charge',
      label: {
        show: false,
      },
      data: shoulderCharge,
    },
    {
      name: '平充电量',
      type: 'bar',
      stack: 'charge',
      label: {
        show: false,
      },
      data: flatCharge,
    },
    {
      name: '谷充电量',
      type: 'bar',
      stack: 'charge',
      label: {
        show: false,
      },
      data: valleyCharge,
    },
    {
      name: '深谷充电量',
      type: 'bar',
      stack: 'charge',
      label: {
        show: false,
      },
      data: deepValleyCharge,
    },
    // 折线图 - 电费
    {
      name: '尖电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: peakFee,
    },
    {
      name: '峰电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: shoulderFee,
    },
    {
      name: '平电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: flatFee,
    },
    {
      name: '谷电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: valleyFee,
    },
    {
      name: '深谷电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: deepValleyFee,
    },
  ]

  return {
    color: ['#2090ff', '#fbba4f', '#55cd92', '#4ebaf8', '#8ddbcf', '#2090ff', '#fbba4f', '#55cd92', '#4ebaf8', '#8ddbcf'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter(params: any) {
        let rValue = `${params[0].name}<br>`
        params.forEach((v: any) => {
          if (v.data !== 0 && v.data !== '-' && v.data !== undefined) {
            const unit = v.seriesName.includes('电费') ? '元' : 'kwh'
            rValue += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${v.color}"></span>${v.seriesName}: ${v.data}${unit}<br>`
          }
        })
        return rValue
      },
    },
    legend: [
      {
        icon: 'circle',
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 10,
        data: ['尖充电量', '峰充电量', '平充电量', '谷充电量', '深谷充电量'],
        right: '10%',
        top: '2%',
        textStyle: {
          fontSize: 12,
          color: themeVars.value.textColor1,
        },
      },
      {
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 10,
        data: ['尖电费', '峰电费', '平电费', '谷电费', '深谷电费'],
        right: '10%',
        top: '10%',
        textStyle: {
          fontSize: 12,
          color: themeVars.value.textColor1,
        },
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: times,
      axisLabel: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '单位: kwh',
        nameTextStyle: {
          color: themeVars.value.textColor1,
        },
        axisLabel: {
          color: themeVars.value.textColor1,
        },
        axisLine: {
          lineStyle: {
            color: themeVars.value.textColor1,
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: themeVars.value.borderColor,
          },
        },
      },
      {
        type: 'value',
        name: '单位: 元',
        nameTextStyle: {
          color: themeVars.value.textColor1,
        },
        axisLabel: {
          color: themeVars.value.textColor1,
        },
        axisLine: {
          lineStyle: {
            color: themeVars.value.textColor1,
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    series,
  }
})

useEcharts('chargingChart', chartOptions)
</script>

<template>
  <BCard title="充电花费分析">
    <template #extend>
      <div class="flex items-center gap-2">
        <NDatePicker
          v-model:value="startTime"
          type="datetimerange"
          placeholder="开始时间"
          size="small"
        />
        <NRadioGroup v-model:value="activeTab" size="small" type="line">
          <NRadioButton value="day" label="日" />
          <NRadioButton value="month" label="月" />
          <NRadioButton value="year" label="年" />
        </NRadioGroup>
      </div>
    </template>
    <div ref="chargingChart" class="w-full h-full" />
  </BCard>
</template>

<style scoped>
:deep(.n-card__content) {
  height: calc(100% - 50px);
}
</style>

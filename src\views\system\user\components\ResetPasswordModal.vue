<script setup lang="ts">
import { useBoolean } from '@/hooks'
import { resetUserPwd } from '@/service/api/system/user'
import type { ResetPwdParams } from '@/service/api/system/user'

const emit = defineEmits<{
  success: []
}>()

const { bool: visible, setTrue: show, setFalse: hide } = useBoolean(false)
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// 表单数据
const formModel = ref<ResetPwdParams>({
  userId: 0,
  password: '',
})

// 用户信息
const currentUser = ref<Entity.UserInfo | null>(null)

const formRef = ref()

// 表单验证规则
const rules = {
  password: {
    required: true,
    message: '请输入新密码',
    trigger: ['blur'],
    min: 6,
  },
}

// 打开弹窗
function openModal(user: Entity.UserInfo) {
  currentUser.value = user
  formModel.value = {
    userId: user.userId,
    password: '',
  }
  show()
}

// 关闭弹窗
function closeModal() {
  hide()
  formModel.value = {
    userId: 0,
    password: '',
  }
  currentUser.value = null
}

// 提交重置密码
async function handleSubmit() {
  try {
    // 验证表单
    await formRef.value?.validate()

    startLoading()
    await resetUserPwd(formModel.value)
    window.$message.success('密码重置成功')
    emit('success')
    closeModal()
  }
  catch (error) {
    console.error('密码重置失败:', error)
  }
  finally {
    endLoading()
  }
}

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="重置密码"
    class="w-400px"
    :mask-closable="false"
    :segmented="{
      content: true,
      footer: true,
    }"
  >
    <div v-if="currentUser" class="mb-2">
      <n-text>
        请输入用户 <n-text type="primary" strong>
          {{ currentUser!.userName }}
        </n-text> 的新密码
      </n-text>
    </div>

    <n-form
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-placement="left"
    >
      <n-form-item path="password">
        <n-input
          v-model:value="formModel.password"
          type="password"
          placeholder="请输入新密码"
          show-password-on="click"
          clearable
        />
      </n-form-item>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="closeModal">
          取消
        </n-button>
        <n-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          确定
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

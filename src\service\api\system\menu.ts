import { request } from '../../http'
// 获取菜单列表
export function listMenu(params?: any) {
  return request.Get<Api.ResponseWithData<Entity.Menu[]>>('/system/menu/list', { params })
}

// 查询菜单详细
export function getMenu(menuId: string) {
  return request.Get<Api.ResponseWithData<Entity.Menu>>(`/system/menu/${menuId}`)
}

// 查询菜单下拉树结构
export function treeselect() {
  return request.Get<Api.ResponseWithData<Api.TreeNode[]>>(`/system/menu/treeselect`)
}

// 新增菜单
export function addMenu(data: Partial<Entity.Menu>) {
  return request.Post<Api.BaseResponse>('/system/menu', data)
}

// 修改菜单
export function updateMenu(data: Partial<Entity.Menu>) {
  return request.Put<Api.BaseResponse>('/system/menu', data)
}

// 删除菜单
export function delMenu(menuId: string) {
  return request.Delete<Api.BaseResponse>(`/system/menu/${menuId}`)
}

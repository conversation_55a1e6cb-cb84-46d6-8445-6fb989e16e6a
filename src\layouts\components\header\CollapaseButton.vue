<script setup lang="ts">
import { useAppStore } from '@/store'

const appStore = useAppStore()
</script>

<template>
  <n-tooltip placement="bottom" trigger="hover">
    <template #trigger>
      <CommonWrapper @click="appStore.toggleCollapse()">
        <!-- <nova-icon v-if="appStore.collapsed" icon="carbon:chevron-right" /> -->
        <icon-park-outline-right v-if="appStore.collapsed" />
        <icon-park-outline-left v-else />
      </CommonWrapper>
    </template>
    <span>{{ $t('app.toggleSider') }}</span>
  </n-tooltip>
</template>

<style scoped></style>

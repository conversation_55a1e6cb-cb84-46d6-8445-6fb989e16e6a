<script setup lang="ts">
import Background from '@/assets/imgs/energy-storage/map/number-background.png'

defineProps({
  value: {
    type: Number,
    validator(value: number): boolean {
      return value >= 0 && value < 10
    },
  },
})
</script>

<template>
  <div class="numberItem w-[27px] h-[35px] text-center line-height-[35px]" :style="{ backgroundImage: `url(${Background})` }">
    <n-text class="text-xl" style="color:var(--primary-color);vertical-align: middle">
      {{ value }}
    </n-text>
  </div>
</template>

<style scoped>
.numberItem {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>

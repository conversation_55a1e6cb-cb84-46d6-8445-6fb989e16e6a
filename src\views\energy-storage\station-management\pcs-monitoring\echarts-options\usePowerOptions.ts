import type { ECOption } from '@/hooks'
import dayjs from 'dayjs'
import { useThemeVars } from 'naive-ui'

export function usePowerOptions() {
  const themeVars = useThemeVars()
  return computed(() => {
    // 使用dayjs生成时间序列数据
    const now = dayjs()
    const xAxisData = Array.from({ length: 28 }, (_, i) => {
      return now.subtract(i, 'day').format('YYYY-MM-DD HH:mm') // 从当前时间向前后各14小时
    })

    return {
      // 定义全局颜色，确保线条和图例颜色一致
      color: ['#91cfac', '#e8c179', '#3f9cf4'],
      grid: {
        top: 30,
        left: 70,
        right: 40,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          formatter: (value: string, index: number) => {
            // 每隔4个显示一个标签，避免过于拥挤
            return index % 4 === 0 ? value : ''
          },
          // rotate: 45 // 标签旋转45度，防止重叠
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value} kW', // 添加单位
        },
        splitLine: {
          lineStyle: {
            // 使用深浅的间隔色
            color: [`${themeVars.value.primaryColor}33`],
          },
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        formatter: (params: any) => {
          const date = params[0].axisValue
          let result = `${date}<br>`
          params.forEach((item: any) => {
            result += `${item.seriesName || item.seriesIndex}: ${item.value[1]} kW<br>`
          })
          return result
        },
      },
      dataZoom: [
        {
          show: true,
          type: 'inside',
          filterMode: 'none',
          xAxisIndex: [0],
          startValue: xAxisData[4], // 默认显示部分数据
          endValue: xAxisData[24],
        },
        {
          show: true,
          type: 'inside',
          filterMode: 'none',
          yAxisIndex: [0],
          startValue: -20,
          endValue: 20,
        },
      ],
      series: [
        {
          name: '交流有功',
          type: 'line',
          showSymbol: false,
          clip: true,
          data: [0, 0, 0, 0, 2, 5, 6, 8, 20, 20, 20, 8, 6, 2, 0, 0, 0, 0, 2, 6, 10, 10, 10, 5, 0, 0, 0, 0].map((value, index) => [xAxisData[index], value]),
          lineStyle: {
            type: 'dashed',
          },
        },
        {
          name: '交流无功',
          type: 'line',
          showSymbol: false,
          clip: true,
          data: [0, 0, 0, 0, -2, -5, -6, -8, -20, -20, -20, -8, -6, -2, 0, 0, 0, 0, -2, -6, -10, -10, -10, -5, 0, 0, 0].map((value, index) => [xAxisData[index], value]),
          lineStyle: {
            type: 'dashed',
          },
        },
        {
          name: '交流现在',
          type: 'line',
          showSymbol: false,
          clip: true,
          data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0].map((value, index) => [xAxisData[index], value]),
          lineStyle: {
            type: 'dashed',
          },
        },
      ],
      legend: {},
    } as ECOption
  })
}

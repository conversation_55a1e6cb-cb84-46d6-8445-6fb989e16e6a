import { request } from '../../http'

// 角色查询参数类型
export interface RoleQueryParams {
  roleName?: string
  roleKey?: string
  status?: '0' | '1'
  params?: {
    beginTime?: string
    endTime?: string
  }
  pageNum: number
  pageSize: number
}

// 角色状态修改参数
export interface ChangeRoleStatusParams {
  roleId: number
  status: '0' | '1'
}

// 数据权限配置参数
export interface DataScopeParams {
  roleId: number
  dataScope: string
  deptIds?: number[]
  menuCheckStrictly?: boolean
  deptCheckStrictly?: boolean
}

// 用户授权参数
export interface AuthUserParams {
  roleId: number
  userId: number
}
export interface AuthUserAllParams {
  roleId: number
  userIds: string
}

// 查询角色列表
export function listRole(params: RoleQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.Role[]>>('/system/role/list', { params })
}

// 查询角色详细
export function getRole(roleId: number) {
  return request.Get<Api.ResponseWithData<Entity.Role>>(`/system/role/${roleId}`)
}

// 新增角色
export function addRole(data: Omit<Entity.Role, 'roleId' | 'createTime' | 'updateTime'>) {
  return request.Post<Api.BaseResponse>('/system/role', data)
}

// 修改角色
export function updateRole(data: Entity.Role) {
  return request.Put<Api.BaseResponse>('/system/role', data)
}

// 角色数据权限
export function dataScope(data: DataScopeParams) {
  return request.Put<Api.BaseResponse>('/system/role/dataScope', data)
}

// 角色状态修改
export function changeRoleStatus(params: ChangeRoleStatusParams) {
  return request.Put<Api.BaseResponse>('/system/role/changeStatus', params)
}

// 删除角色
export function delRole(roleId: string) {
  return request.Delete<Api.BaseResponse>(`/system/role/${roleId}`)
}

// 查询角色已授权用户列表
export function allocatedUserList(params: RoleQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.UserInfo[]>>('/system/role/authUser/allocatedList', { params })
}

// 查询角色未授权用户列表
export function unallocatedUserList(params: RoleQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.UserInfo[]>>('/system/role/authUser/unallocatedList', { params })
}

// 取消用户授权角色
export function authUserCancel(data: AuthUserParams) {
  return request.Put<Api.BaseResponse>('/system/role/authUser/cancel', data)
}

// 批量取消用户授权角色
export function authUserCancelAll(params: AuthUserAllParams) {
  return request.Put<Api.BaseResponse>('/system/role/authUser/cancelAll', undefined, { params })
}

// 授权用户选择
export function authUserSelectAll(params: AuthUserAllParams) {
  return request.Put<Api.BaseResponse>('/system/role/authUser/selectAll', undefined, { params })
}

// 根据角色ID查询部门树结构
interface DeptTreeselect extends Api.BaseResponse {
  depts: Api.TreeNode[]
  checkedKeys: number[]
}
export function roleDeptTreeSelect(roleId: number) {
  return request.Get<DeptTreeselect>(`/system/role/deptTree/${roleId}`)
}

// 根据角色ID查询菜单下拉树结构
interface MenuTreeselect extends Api.BaseResponse {
  menus: Api.TreeNode[]
  checkedKeys: number[]
}
export function roleMenuTreeselect(roleId: string) {
  return request.Get<MenuTreeselect>(`/system/menu/roleMenuTreeselect/${roleId}`)
}

<script setup lang="ts">
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import Charge from '@/assets/imgs/energy-storage/map/charge.png'
import Discharge from '@/assets/imgs/energy-storage/map/discharge.png'
import OverviewItem from './common/OverviewItem.vue'
import { useEchartsOptions } from '../echarts-options/ElectricityOverview'

const echartsOptions = useEchartsOptions()

enum Type {
  Today = '日',
  Cumulative = '总',
}

const type = ref(Type.Today)

useEcharts('echarts', echartsOptions)
</script>

<template>
  <BCard class="h-[384px]" title="电力概况">
    <header class="flex justify-between items-center gap-4 mb-3">
      <OverviewItem class="flex-1" :icon="Charge" name="总充电量" value="132" unit="KWh" />
      <OverviewItem class="flex-1" :icon="Discharge" name="总放电量" value="132" unit="KWh" />
    </header>
    <n-space class="px-2" justify="space-between" align="center">
      <n-space align="center" :size="0">
        <n-el class="w-[3px] h-[11px]" style="background-color: var(--primary-color)" />
        <n-el
          class="w-[3px] h-[11px]"
          style="background-color: color-mix(in srgb,var(--primary-color) 20%,white 30%)"
        />
        <n-text class="ml-2">
          {{ type }}充放电量
        </n-text>
      </n-space>
      <n-button-group size="tiny">
        <n-button :type="type === Type.Today ? 'info' : undefined" @click="type = Type.Today">
          今日
        </n-button>
        <n-button :type="type === Type.Cumulative ? 'info' : undefined" @click="type = Type.Cumulative">
          累计
        </n-button>
      </n-button-group>
    </n-space>
    <main ref="echarts" class="h-[calc(100%-94px)] w-full " />
  </BCard>
</template>

<style scoped>

</style>

<script setup lang="tsx">
import type { MenuOption } from 'naive-ui'
import { useAppStore, useRouteStore } from '@/store'
import {
  BackTop,
  Breadcrumb,
  CollapaseButton,
  ContentFullScreen,
  Logo,
  MobileDrawer,
  // Notices,
  Search,
  Setting,
  SettingDrawer,
  TabBar,
  UserCenter,
} from './components'
import Content from './Content.vue'
import { ProLayout, useLayoutMenu } from 'pro-naive-ui'

const route = useRoute()
const appStore = useAppStore()
const routeStore = useRouteStore()

const { layoutMode } = storeToRefs(useAppStore())
const appName = import.meta.env.VITE_APP_NAME
const {
  layout,
  activeKey,
} = useLayoutMenu({
  mode: layoutMode,
  accordion: true,
  menus: routeStore.menus,
})

watch(() => route.path, () => {
  activeKey.value = routeStore.activeMenu
}, { immediate: true })

// 移动端抽屉控制
const showMobileDrawer = ref(false)

const sidebarWidth = ref(240)
const sidebarCollapsedWidth = ref(64)
const navHeight = ref(60)

const hasHorizontalMenu = computed(() => ['horizontal', 'mixed-two-column', 'mixed-sidebar'].includes(layoutMode.value))

const hidenCollapaseButton = computed(() => ['horizontal'].includes(layoutMode.value) || appStore.isMobile)

function siderMenuRender(option: MenuOption) {
  return (
    <div class="flex flex-col items-center justify-center">
      {option.icon?.()}
      <span class="whitespace-nowrap text-3">{option.label()}</span>
    </div>
  )
}

// 判断是否需要自定义图标渲染
const needCustomIconRender = computed(() => ['two-column', 'mixed-two-column'].includes(layoutMode.value))
</script>

<template>
  <SettingDrawer />
  <ProLayout
    v-model:collapsed="appStore.collapsed"
    :builtin-theme-overrides="{
      layoutColor: appStore.colorMode === 'dark' ? '#181818' : '#EDF4F4',
      color: 'transparent',
    }"
    :mode="layoutMode"
    :is-mobile="appStore.isMobile"
    :show-logo="appStore.showLogo && !appStore.isMobile"
    :show-footer="appStore.showFooter"
    :show-tabbar="appStore.showTabs"
    nav-fixed
    show-nav
    show-sidebar
    :nav-height="navHeight"
    :tabbar-height="45"
    :footer-height="40"
    :sidebar-width="sidebarWidth"
    :sidebar-collapsed-width="sidebarCollapsedWidth"
  >
    <template #logo>
      <Logo />
    </template>

    <template #nav-left>
      <template v-if="appStore.isMobile">
        <Logo />
      </template>

      <template v-else>
        <div v-if="!hasHorizontalMenu || !hidenCollapaseButton" class="h-full flex-y-center gap-3 p-x-sm">
          <CollapaseButton v-if="!hidenCollapaseButton" />
          <Breadcrumb v-if="!hasHorizontalMenu" />
        </div>
      </template>
    </template>

    <template #nav-center>
      <div class="h-full flex-y-center gap-1">
        <n-menu v-if="hasHorizontalMenu" v-bind="layout.horizontalMenuProps" />
      </div>
    </template>

    <template #nav-right>
      <div class="h-full flex-y-center gap-3 p-x-xl">
        <!-- 移动端：只显示菜单按钮 -->
        <template v-if="appStore.isMobile">
          <n-button
            quaternary
            @click="showMobileDrawer = true"
          >
            <template #icon>
              <n-icon size="18">
                <icon-park-outline-hamburger-button />
              </n-icon>
            </template>
          </n-button>
        </template>

        <!-- 桌面端：显示完整功能组件 -->
        <template v-else>
          <Search />
          <!-- <Notices /> -->
          <ContentFullScreen />
          <DarkModeSwitch />
          <!-- <LangsSwitch /> -->
          <Setting />
          <UserCenter />
        </template>
      </div>
    </template>

    <template #sidebar>
      <template v-if="needCustomIconRender">
        <n-menu
          style="--n-item-height:52px;"
          v-bind="layout.verticalMenuProps"
          :collapsed-width="sidebarCollapsedWidth"
          :render-icon="siderMenuRender"
          inverted
        />
      </template>
      <template v-else>
        <n-menu v-bind="layout.verticalMenuProps" :collapsed-width="sidebarCollapsedWidth" inverted />
      </template>
    </template>

    <template #sidebar-extra>
      <n-scrollbar class="flex-[1_0_0]">
        <div class="flex-center" :style="{ height: `${navHeight}px` }">
          <n-text type="primary" strong class="text-2xl">
            {{ appName }}
          </n-text>
        </div>
        <n-menu
          v-bind="layout.verticalExtraMenuProps" :collapsed-width="sidebarCollapsedWidth" inverted
        />
      </n-scrollbar>
    </template>

    <template #tabbar>
      <TabBar />
    </template>

    <template #footer>
      <div class="flex-center h-full">
        {{ appStore.footerText }}
      </div>
    </template>
    <Content />
    <BackTop />
    <SettingDrawer />

    <!-- 移动端功能抽屉 -->
    <MobileDrawer v-model:show="showMobileDrawer">
      <n-menu v-bind="layout.verticalMenuProps" />
    </MobileDrawer>
  </ProLayout>
</template>

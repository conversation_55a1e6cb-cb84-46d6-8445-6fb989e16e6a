<script setup lang="ts">
import { computed, ref } from 'vue'
import { NDatePicker, useThemeVars } from 'naive-ui'

import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import type { ECOption } from '@/hooks/useEcharts'

const startYear = ref<number | null>(new Date().getFullYear())
const themeVars = useThemeVars()

const chartOptions = computed<ECOption>(() => {
  // 年度数据
  const times = ['01:30', '01:31', '01:32', '01:33']
  const chargeVolume = [45, 38, 52, 65]
  const dischargeVolume = [40, 35, 60, 80]
  const revenue = [12, 10, 18, 25]

  // 定义统一的颜色
  const revenueColor = 'rgba(255,165,0,0.8)' // #ffa500 橙色

  // 构建系列数据
  const series: any[] = [
    {
      name: '充电量',
      type: 'bar',
      barGap: '10%',
      barWidth: 15,
      label: {
        show: false,
      },
      itemStyle: {
        borderRadius: [15, 15, 0, 0],
      },
      data: chargeVolume,
    },
    {
      name: '放电量',
      type: 'bar',
      barGap: '10%',
      barWidth: 15,
      label: {
        show: false,
      },
      itemStyle: {
        borderRadius: [15, 15, 0, 0],
      },
      data: dischargeVolume,
    },
    {
      name: '收益',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      label: {
        show: false,
      },
      lineStyle: {
        color: revenueColor,
        width: 3,
      },
      itemStyle: {
        color: revenueColor,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(255,165,0,0.3)' },
            { offset: 1, color: 'rgba(255,165,0,0.1)' },
          ],
          globalCoord: false,
        },
      },
      data: revenue,
    },
  ]

  return {
    color: ['#51a8f6', '#53ce8e'],
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        let rValue = `${params[0].name}<br>`
        params.forEach((v: any) => {
          if (v.data !== 0 && v.data !== '-' && v.data !== undefined) {
            const unit = v.seriesName === '收益' ? '元' : 'kwh'
            rValue += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${v.color}"></span>${v.seriesName}: ${v.data}${unit}<br>`
          }
        })
        return rValue
      },
    },
    legend: {
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 15,
      data: ['充电量', '放电量', '收益'],
      right: '4%',
      textStyle: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: times,
      axisLabel: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '单位: kwh',
      nameTextStyle: {
        color: themeVars.value.textColor1,
      },
      axisLabel: {
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    series,
  }
})

useEcharts('annualChart', chartOptions)
</script>

<template>
  <BCard title="年收益">
    <template #extend>
      <div class="flex items-center gap-2">
        <NDatePicker
          v-model:value="startYear"
          type="yearrange"
          placeholder="开始年"
          size="small"
        />
      </div>
    </template>
    <div ref="annualChart" class="w-full h-full" />
  </BCard>
</template>

<style scoped>
:deep(.n-card__content) {
  height: calc(100% - 50px);
}
</style>

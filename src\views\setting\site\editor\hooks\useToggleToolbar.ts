import { useVueFlow } from '@vue-flow/core'
import { topologyMapId } from '@/views/setting/site/editor/hooks/useControl'

export function useToggleToolbar() {
  const { onNodeClick, onPaneClick, findNode } = useVueFlow(topologyMapId)

  const nodeId = ref<string | null>(null)

  function tryClose() {
    if (nodeId.value) {
      const node = findNode(nodeId.value)
      if (node && node.data && !!node.data.toolbarVisible) {
        node.data.toolbarVisible = !node.data.toolbarVisible
      }
    }
  }

  onNodeClick(({ node }) => {
    if (node.id !== nodeId.value) {
      tryClose()
    }

    node.data.toolbarVisible = !node.data.toolbarVisible
    nodeId.value = node.id
  })

  onPaneClick(() => tryClose())
}

<script setup lang="ts">
import type { ECOption } from '@/hooks'
import { useEcharts } from '@/hooks'

// 响应式数据长度，用于动态测试
const dataLength = 30

// 计算标签和数据
const xLabel = Array.from({ length: dataLength }, (_, i) => `${i + 1}#电芯`)
const chartData = Array.from({ length: dataLength }, () => (Math.random() * 10).toFixed(2))

const chatOptions = computed<ECOption>(() => {
  return {
    // 定义全局颜色，确保线条和图例颜色一致
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '50px', // 根据是否显示 dataZoom 调整底部空间
      top: '10%',
      containLabel: true,
    },
    dataZoom: [
      {
        show: true,
        start: 0,
        end: 100,
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: xLabel,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        interval: 0, // 强制显示所有标签
        rotate: 0, // 根据策略旋转标签
        fontSize: 12,
        color: '#666',
        margin: 8,
        // 当标签过长时自动换行或截断
        formatter: (value: string) => {
          if (value.length > 6) {
            return `${value.substring(0, 6)}...`
          }
          return value
        },
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#CFE6FC',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
      },
    },
    series: [
      {
        color: '#51A8F6',
        name: '单体温度平均值',
        type: 'bar',
        barWidth: '20%', // 使用百分比，自适应宽度
        barMaxWidth: 30, // 最大宽度限制
        data: chartData,
        itemStyle: {
          borderRadius: [2, 2, 0, 0], // 顶部圆角
        },
        emphasis: {
          itemStyle: {
            color: '#3A8EE6',
          },
        },
        animationDelay: (idx: number) => idx * 10, // 动画延迟
      },
    ],
  }
})

useEcharts('echarts', chatOptions)
</script>

<template>
  <BCard title="单体温度/电压分布">
    <template #extend>
      <n-space>
        <n-input-number
          v-model:value="dataLength"
          :min="5"
          :max="100"
          size="small"
          placeholder="数据量"
          style="width: 100px"
        />
        <n-date-picker type="datetime" size="small" />
        <n-button type="primary" size="small">
          查询
        </n-button>
      </n-space>
    </template>

    <div ref="echarts" class="h-full" />
  </BCard>
</template>

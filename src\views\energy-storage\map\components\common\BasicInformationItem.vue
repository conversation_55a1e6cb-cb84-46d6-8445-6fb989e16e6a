<script setup lang="ts">
defineProps<{
  icon: string
  name: string
  value: string
  unit: string
}>()
</script>

<template>
  <div class="flex gap-2">
    <img class="basis-[56px] flex-nowrap" width="56" height="56" :src="icon" alt="icon">
    <div class="whitespace-nowrap">
      <n-p class="m-0">
        {{ name }}
      </n-p>
      <n-p class="m-0" style="color: var(--primary-color)">
        <span class="text-xl font-bold">{{ value }}</span>
        <span class="text-sm">{{ unit }}</span>
      </n-p>
    </div>
  </div>
</template>

<style scoped>

</style>

<script setup lang="ts">
import BHeader from '@/components/big-screen/BHeader.vue'
import WaterImmersionEmergencyStop from './components/WaterImmersionEmergencyStop.vue'
import Cooler from '@/views/energy-storage/station-management/security-monitoring/components/Cooler.vue'
import GasSensorStatus
  from '@/views/energy-storage/station-management/security-monitoring/components/GasSensorStatus.vue'
import TemperatureMonitoring
  from '@/views/energy-storage/station-management/security-monitoring/components/TemperatureMonitoring.vue'
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
</script>

<template>
  <EnergyStorageBackground class="h-full">
    <BHeader title="分布式储能运营云平台" />
    <NSelect class="w-[120px] mx-3 mb-3" size="small" placeholder="场站选择" />
    <main class="grid grid-cols-3 grid-rows-2 h-[calc(100%-80px-40px)] flex relative z-1 p-3 pt-0 gap-3">
      <WaterImmersionEmergencyStop />
      <Cooler />
      <GasSensorStatus />
      <TemperatureMonitoring class="col-span-3" />
    </main>
  </EnergyStorageBackground>
</template>

/// <reference path="../global.d.ts"/>
namespace Entity {
  interface Maintenance {
    /** 主键 */
    id: number
    /** 站点编号 */
    siteId: string
    /** 维护类型 */
    maintenanceType: string
    /** 设备型号 */
    deviceModel: string
    /** 设备类型 */
    deviceType: string
    /** 维护时间 */
    maintenanceTime: string
    /** 维护周期 */
    maintenanceCycle: string
    /** 维护信息 */
    maintenanceInfo: string
    /** 设备名称 */
    deviceName: string
    /** 单位 */
    unit: string
    /** 设备编号 */
    deviceNumber: string
    /** 规格 */
    specification: string
    /** 生产厂家 */
    producer: string
    /** 创建人 */
    createBy?: string
    /** 创建时间 */
    createTime?: string
    /** 更新人 */
    updateBy?: string
    /** 更新时间 */
    updateTime?: string
    /** 删除标志 */
    delFlag?: string
    /** 数据范围 */
    dataScope?: string
    /** 参数 */
    params?: any
    /** 备注 */
    remark?: string
  }
}

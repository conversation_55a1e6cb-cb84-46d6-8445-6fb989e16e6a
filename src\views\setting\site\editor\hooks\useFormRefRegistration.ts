// TODO DELETE
import type { FormInst } from 'naive-ui'

export const PrivatePropertyFormKey = 'privatePropertyFormRef'

export const FormInstKey = 'formInstKey'

export type FormInstProvide = Set<FormInst>

/**
 * @description 注册表单，用于统一校验，默认表单ref是 PrivatePropertyFormKey
 */
export function useFormRefRegistration(key = PrivatePropertyFormKey) {
  const ref = useTemplateRef<FormInst>(key)

  const formInst = useInjectFormInst()

  onMounted(() => {
    if (ref.value === null) {
      console.warn('没有表单需要注册!')
    }
    if (formInst && formInst.value && ref.value) {
      formInst.value.add(ref.value)
    }
  })

  onUnmounted(() => {
    if (ref.value && formInst.value.has(ref.value)) {
      formInst.value.delete(ref.value)
    }
  })
}

export function useFormRegistrationProvide() {
  provide<Ref<FormInstProvide>>(FormInstKey, ref<FormInstProvide>(new Set()))
}

// 验证表单
export function useFormCollection() {
  const list = useInjectFormInst()

  async function validateAll() {
    const validateList = Array.from(list.value)
      .map(formInst => formInst.validate())

    await Promise.all(validateList)
  }

  return { list, validateAll }
}

export function useInjectFormInst() {
  return inject<Ref<FormInstProvide>>(FormInstKey, ref(new Set<FormInst>()))
}

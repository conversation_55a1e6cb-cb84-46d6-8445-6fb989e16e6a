<script setup lang="ts">
import { computed, ref } from 'vue'
import { NRadioButton, NRadioGroup, useThemeVars } from 'naive-ui'

import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import type { ECOption } from '@/hooks/useEcharts'

const activeTab = ref('daily')
const themeVars = useThemeVars()

const chartOptions = computed<ECOption>(() => {
  // 根据选中的标签生成不同的数据
  let times, chargeVolume, dischargeVolume, revenue

  switch (activeTab.value) {
    case 'daily':
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      chargeVolume = [45, 38, 52, 65, 42, 58, 48]
      dischargeVolume = [40, 35, 60, 80, 45, 70, 55]
      revenue = [12, 10, 18, 25, 15, 22, 18]
      break
    case 'monthly':
      times = ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
      chargeVolume = [1200, 1350, 1100, 1400, 1300, 1250, 1200]
      dischargeVolume = [1150, 1300, 1050, 1350, 1250, 1200, 1150]
      revenue = [350, 380, 320, 400, 370, 350, 340]
      break
    default:
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      chargeVolume = [45, 38, 52, 65, 42, 58, 48]
      dischargeVolume = [40, 35, 60, 80, 45, 70, 55]
      revenue = [12, 10, 18, 25, 15, 22, 18]
  }

  // 定义统一的颜色
  const chargeColor = 'rgba(81,168,246,0.7)' // #51a8f6 蓝色
  const dischargeColor = 'rgba(83,206,142,0.7)' // #53ce8e 绿色
  const revenueColor = 'rgba(255,165,0,0.8)' // #ffa500 橙色

  // 构建系列数据
  const series: any[] = [
    {
      name: '充电量',
      type: 'bar',
      barGap: '10%',
      barWidth: 15,
      label: {
        show: false,
      },
      itemStyle: {
        color: chargeColor,
        borderRadius: [15, 15, 0, 0],
      },
      data: chargeVolume,
    },
    {
      name: '放电量',
      type: 'bar',
      barGap: '10%',
      barWidth: 15,
      label: {
        show: false,
      },
      itemStyle: {
        color: dischargeColor,
        borderRadius: [15, 15, 0, 0],
      },
      data: dischargeVolume,
    },
    {
      name: '收益',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      label: {
        show: false,
      },
      lineStyle: {
        color: revenueColor,
        width: 3,
      },
      itemStyle: {
        color: revenueColor,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(255,165,0,0.3)' },
            { offset: 1, color: 'rgba(255,165,0,0.1)' },
          ],
          globalCoord: false,
        },
      },
      data: revenue,
    },
  ]

  return {
    color: ['#51a8f6', '#53ce8e', '#ffa500'],
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        let rValue = `${params[0].name}<br>`
        params.forEach((v: any) => {
          if (v.data !== 0 && v.data !== '-' && v.data !== undefined) {
            const unit = v.seriesName === '收益' ? '元' : 'kwh'
            rValue += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${v.color}"></span>${v.seriesName}: ${v.data}${unit}<br>`
          }
        })
        return rValue
      },
    },
    legend: {
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 15,
      data: ['充电量', '放电量', '收益'],
      right: '4%',
      textStyle: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: times,
      axisLabel: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '单位: kwh',
      nameTextStyle: {
        color: themeVars.value.textColor1,
      },
      axisLabel: {
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    series,
  }
})

useEcharts('dailyMonthlyChart', chartOptions)
</script>

<template>
  <BCard title="收益概况">
    <template #extend>
      <NRadioGroup v-model:value="activeTab" size="small" type="line">
        <NRadioButton value="daily" label="日收益" />
        <NRadioButton value="monthly" label="月收益" />
      </NRadioGroup>
    </template>
    <div ref="dailyMonthlyChart" class="w-full h-full" />
  </BCard>
</template>

<style scoped>
:deep(.n-card__content) {
  height: calc(100% - 50px);
}
</style>

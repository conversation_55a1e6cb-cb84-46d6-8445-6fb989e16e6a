<script setup lang="ts">
import Step1 from '@/views/setting/site/editor/components/Step1.vue'
import Step2 from '@/views/setting/site/editor/components/Step2.vue'
import Step3 from '@/views/setting/site/editor/components/Step3.vue'
import { useStore } from '@/views/setting/site/editor/useStore'

const store = useStore()

const route = useRoute()

onMounted(() => {
  if (route.query && route.query.siteId) {
    store.load(route.query.siteId)
  }
  else {
    store.$reset()
  }
})
</script>

<template>
  <div class="wh-full">
    <main class="h-full w-full">
      <Suspense v-if="store.step === 1">
        <Step1 />
        <template #fallback>
          Loading...
        </template>
      </Suspense>
      <Step2 v-else-if="store.step === 2" />
      <Step3 v-else-if="store.step === 3" />
    </main>
  </div>
</template>

<style scoped>

</style>

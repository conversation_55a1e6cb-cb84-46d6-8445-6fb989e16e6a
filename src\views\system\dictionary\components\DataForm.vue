<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { addData, getData, updateData } from '@/service/api/system/dict-data'

const props = defineProps<{
  modalName: string
}>()

const emit = defineEmits<{
  success: []
}>()

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

const modalForm = createProModalForm<Partial<Entity.DictData>>({
  omitEmptyString: false,
  initialValues: {
    dictLabel: '',
    dictValue: '',
    dictType: '',
    cssClass: '',
    listClass: 'default',
    dictSort: 0,
    status: '0',
    remark: '',
  },
  onSubmit: handleSubmit,
})

type ModalType = 'add' | 'edit'
const modalType = shallowRef<ModalType>('add')
const modalTitle = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '添加',
    edit: '编辑',
  }
  return `${titleMap[modalType.value]}${props.modalName}`
})

// 提交表单
async function handleSubmit(filedValues: Partial<Entity.DictData>) {
  try {
    startLoading()

    if (modalType.value === 'add') {
      await addData({
        ...filedValues,
        dictType: modalForm.values.value.dictType,
      })
      window.$message.success('新增字典数据成功')
    }
    else {
      await updateData({
        ...filedValues,
        dictCode: modalForm.values.value.dictCode,
      })
      window.$message.success('修改字典数据成功')
    }

    modalForm.close()
    emit('success')
  }
  catch (error) {
    console.error('提交失败:', error)
    window.$message.error('提交失败')
  }
  finally {
    endLoading()
  }
}

// 打开弹窗
async function openModal(type: ModalType, data: Entity.DictData) {
  modalType.value = type
  modalForm.open()
  const handlers = {
    async add() {
      modalForm.values.value.dictType = data.dictType
    },
    async edit() {
      if (!data)
        return
      // 获取完整的字典数据信息
      const response = await getData(data.dictCode!)
      modalForm.values.value = response.data
    },
  }
  await handlers[type]()
}

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :title="modalTitle"
    :loading="loading"
    width="600px"
  >
    <n-grid cols="2" x-gap="16">
      <n-gi>
        <pro-input title="字典类型" path="dictType" :field-props="{ disabled: true }" />
      </n-gi>

      <n-gi>
        <pro-input title="数据标签" path="dictLabel" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-input title="数据键值" path="dictValue" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-input title="样式属性" path="cssClass" :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-digit title="显示排序" path="dictSort" required :field-props="{ min: 0 }" />
      </n-gi>

      <n-gi>
        <pro-select
          title="回显样式"
          path="listClass"
          :field-props="{
            options: [
              { label: '默认(default)', value: 'default' },
              { label: '主要(primary)', value: 'primary' },
              { label: '成功(success)', value: 'success' },
              { label: '信息(info)', value: 'info' },
              { label: '警告(warning)', value: 'warning' },
              { label: '危险(danger)', value: 'danger' },
            ],
            clearable: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-radio-group
          title="状态"
          path="status"
          :field-props="{
            options: [
              { label: '正常', value: '0' },
              { label: '停用', value: '1' },
            ],
          }"
        />
      </n-gi>

      <n-gi :span="2">
        <pro-textarea
          title="备注"
          path="remark"
          :field-props="{
            placeholder: '请输入内容',
            rows: 3,
          }"
        />
      </n-gi>
    </n-grid>
  </pro-modal-form>
</template>

import { router } from '@/router'
import { getInfo, login } from '@/service'
import { local } from '@/utils'
import { useRouteStore } from './router'
import { useTabStore } from './tab'

interface AuthStatus {
  userInfo: Entity.UserInfo | null
  roles: string[]
  permissions: string[]
}
export const useAuthStore = defineStore('auth-store', {
  state: (): AuthStatus => {
    return {
      userInfo: local.get('userInfo'),
      roles: [],
      permissions: [],
    }
  },
  getters: {
    /** 是否登录 */
    isLogin(state) {
      return Boolean(state.userInfo)
    },
  },
  actions: {
    /* 登录退出，重置用户信息等 */
    async logout() {
      // 清除本地缓存
      this.clearAuthStorage()
      // 清空路由、菜单等数据
      const routeStore = useRouteStore()
      routeStore.resetRouteStore()
      // 清空标签栏数据
      const tabStore = useTabStore()
      tabStore.clearAllTabs()
      // 重置当前存储库
      this.$reset()
      // 重定向到登录页
      router.push({
        name: 'login',
        query: {
          redirect: router.currentRoute.value.fullPath,
        },
      })
    },
    clearAuthStorage() {
      local.remove('accessToken')
      local.remove('refreshToken')
      local.remove('userInfo')
    },

    /* 用户登录 */
    async login(username: string, password: string, code: string, uuid: string) {
      try {
        const { token } = await login({ username, password, code, uuid })

        // 处理登录信息
        await this.handleLoginInfo(token)
        return true
      }
      catch (e) {
        console.warn('[Login Error]:', e)
      }
    },

    /* 处理登录返回的数据 */
    async handleLoginInfo(token: string) {
      local.set('accessToken', token)

      const { user, roles, permissions } = await getInfo()

      local.set('userInfo', user)
      this.userInfo = user
      this.roles = roles
      this.permissions = permissions

      // 添加路由和菜单
      const routeStore = useRouteStore()
      await routeStore.initAuthRoute()

      // 进行重定向跳转
      const route = unref(router.currentRoute)
      const query = route.query as { redirect: string }
      router.push({
        path: query.redirect || '/',
      })
    },
  },
  persist: {
    storage: localStorage,
  },
})

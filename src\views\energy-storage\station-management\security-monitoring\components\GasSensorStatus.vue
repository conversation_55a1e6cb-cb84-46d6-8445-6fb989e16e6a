<script setup lang="tsx">
import { Normal } from '../components/Status'
import type { DataTableColumns } from 'naive-ui'

const columns: DataTableColumns<any> = [
  {
    title: '',
    key: 'index',
    render: (_v, index) => (
      <div class="flex-inline flex-col justify-center items-center">
        <img src="/src/assets/imgs/energy-storage/station-management/gas-sensor-status-number.png" alt="icon" />
        <n-text>
          {index + 1}
          {' '}
          #
        </n-text>
      </div>
    ),
  },
  {
    title: '烟感',
    key: 'smokeDetector',
    render: () => <Normal />,
  },
  {
    title: '温感',
    key: 'warmSensation',
    render: () => <Normal />,
  },
  {
    title: '门禁传感器',
    key: 'accessControl',
    render: () => <Normal />,
  },
  {
    title: () => 'CO浓度值',
    key: 'CO',
    render: () => <n-text type="info">0</n-text>,
  },
  {
    title: '烟雾 浓度值',
    key: 'smoke',
    render: () => <n-text type="info">0</n-text>,
  },
  {
    title: 'VOC 浓度值',
    key: 'VOC',
    render: () => <n-text type="info">0</n-text>,
  },
]
</script>

<template>
  <BCard title="气体传感器状态">
    <NDataTable striped :max-height="300" :columns="columns" :data="new Array(9).fill({})" />
  </BCard>
</template>

<style scoped>

</style>

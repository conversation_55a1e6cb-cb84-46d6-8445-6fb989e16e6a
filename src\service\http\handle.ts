import { fetchUpdateToken } from '@/service'
import { useAuthStore } from '@/store'
import { local } from '@/utils'

/**
 * @description: 处理接口token刷新
 * @return {*}
 */
export async function handleRefreshToken() {
  const authStore = useAuthStore()
  const isAutoRefresh = import.meta.env.VITE_AUTO_REFRESH_TOKEN === 'Y'
  if (!isAutoRefresh) {
    await authStore.logout()
    return
  }

  try {
    // 刷新token
    const { data } = await fetchUpdateToken({ refreshToken: local.get('refreshToken') })
    if (data) {
      local.set('accessToken', data.accessToken)
      local.set('refreshToken', data.refreshToken)
    }
    else {
      // 刷新失败，退出
      console.warn('Token refresh failed: no data returned')
      await authStore.logout()
    }
  }
  catch (error) {
    // 刷新token请求失败，直接退出登录
    console.error('Token refresh request failed:', error)
    await authStore.logout()
  }
}

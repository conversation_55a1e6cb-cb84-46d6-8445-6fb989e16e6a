<script setup lang="ts">
import GridConnectedCabinet from '@/assets/imgs/setting/asset-management/grid-connected-cabinet.png'
import ImageNode from '@/views/setting/site/editor/nodes/ImageNode.vue'
import NodeWrapper from '@/views/setting/site/editor/nodes/NodeWrapper.vue'
import type { NodeProps } from '@vue-flow/core'

defineProps<NodeProps>()
</script>

<template>
  <NodeWrapper v-bind="$props">
    <ImageNode :image="GridConnectedCabinet" :meta="data.meta" />
  </NodeWrapper>
</template>

<style scoped>

</style>

/// <reference path="../global.d.ts"/>

/** 卡号数据库表字段 */
namespace Entity {
  interface Card {
    id: number
    cardNumber: string
    accountOpeningTime: string
    serviceStartTime: string
    serviceStopTime: string
    periodicPackageData: string
    usedData: string
    siteId: number
    createBy?: string
    createTime?: string
    updateBy?: string
    updateTime?: string
    delFlag?: string
    dataScope?: string
    params?: any
    remark?: string
  }
}

import type { DataTableColumns } from 'naive-ui'
import { NButton, NFlex, NSwitch } from 'naive-ui'
import type { ProSearchFormColumns } from 'pro-naive-ui'

// 岗位搜索表单数据类型
export interface PostSearchFormData {
  postCode: string
  postName: string
  status: '0' | '1' | undefined
}

// 岗位搜索表单列配置
export const postSearchColumns: ProSearchFormColumns<PostSearchFormData> = [
  {
    title: '岗位编码',
    path: 'postCode',
    field: 'input',
    fieldProps: {
      placeholder: '请输入岗位编码',
      clearable: true,
    },
  },
  {
    title: '岗位名称',
    path: 'postName',
    field: 'input',
    fieldProps: {
      placeholder: '请输入岗位名称',
      clearable: true,
    },
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' },
      ],
      placeholder: '请选择状态',
      clearable: true,
    },
  },
]

// 岗位表格列配置
export function createPostColumns(options: {
  onEdit: (row: Entity.Post) => void
  onDelete: (postId: number | number[]) => void
  onStatusChange: (row: Entity.Post, status: '0' | '1') => Promise<void>
}): DataTableColumns<Entity.Post> {
  const { onEdit, onDelete, onStatusChange } = options

  return [
    {
      type: 'selection',
      width: 55,
      align: 'center',
    },
    {
      title: '岗位编号',
      key: 'postId',
      align: 'center',
    },
    {
      title: '岗位编码',
      key: 'postCode',
      align: 'center',
    },
    {
      title: '岗位名称',
      key: 'postName',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '岗位排序',
      key: 'postSort',
      align: 'center',
    },
    {
      title: '状态',
      key: 'status',
      align: 'center',
      width: 100,
      render: (row) => {
        return (
          <NSwitch
            value={row.status === '0'}
            onUpdateValue={async (value: boolean) => {
              const newStatus = value ? '0' : '1'
              await onStatusChange(row, newStatus)
            }}
          >
            {{
              checked: () => '启用',
              unchecked: () => '停用',
            }}
          </NSwitch>
        )
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      align: 'center',
      width: 180,
    },
    {
      title: '操作',
      key: 'actions',
      align: 'center',
      width: 160,
      render: (row) => {
        return (
          <NFlex justify="center">
            <NButton
              text
              onClick={() => onEdit(row)}
            >
              编辑
            </NButton>
            <NButton
              text
              type="error"
              onClick={() => onDelete(row.postId!)}
            >
              删除
            </NButton>
          </NFlex>
        )
      },
    },
  ]
}

<script setup lang="ts">
import { ref } from 'vue'
import type { SelectOption } from 'naive-ui'
import { NButton, NDatePicker, NSelect, NSpace, NText } from 'naive-ui'
import { useIntervalFn } from '@vueuse/core'

const stationOptions: SelectOption[] = [
  { label: '站点名称1', value: 'station1' },
  { label: '站点名称2', value: 'station2' },
  { label: '站点名称3', value: 'station3' },
]

const selectedStation = ref('station1')
const startTime = ref<number | null>(Date.now() - 24 * 60 * 60 * 1000) // 默认一天前

// 模拟累计数据
const cumulativeData = ref({
  totalRevenue: 12345.67,
  totalChargeVolume: 5678.90,
  totalDischargeVolume: 5432.10,
})

// 模拟数据更新
useIntervalFn(() => {
  cumulativeData.value.totalRevenue += Math.random() * 100
  cumulativeData.value.totalChargeVolume += Math.random() * 50
  cumulativeData.value.totalDischargeVolume += Math.random() * 45
}, 5000) // 每5秒更新一次

function handleQuery() {
// TODO
}
</script>

<template>
  <div class="flex gap-5 items-start justify-between mr-5">
    <NSpace align="center" justify="start">
      <NSelect
        v-model:value="selectedStation"
        :options="stationOptions"
        placeholder="站点名称"
        size="small"
        class="w-36"
      />
      <NDatePicker
        v-model:value="startTime"
        type="datetimerange"
        placeholder="开始时间"
        size="small"
      />
      <NButton type="primary" size="small" @click="handleQuery">
        查询
      </NButton>
    </NSpace>

    <!-- 累计统计信息 -->
    <div class="flex items-center justify-between h-[28px] bg-[var(--body-color)] gap-3 pr-3">
      <NText class="bg-[var(--primary-color)] color-[var(--body-color)] px-3 h-full line-height-[28px]">
        累计
      </NText>
      <NText>
        历史总收益:
      </NText>
      <NText>
        ¥{{ cumulativeData.totalRevenue.toFixed(2) }}
      </NText>

      <NText>
        总充电量:
      </NText>
      <NText>
        {{ cumulativeData.totalChargeVolume.toFixed(2) }}kwh
      </NText>

      <NText>
        总放电量:
      </NText>
      <NText>
        {{ cumulativeData.totalDischargeVolume.toFixed(2) }}kwh
      </NText>
    </div>
  </div>
</template>

<style scoped>
:deep(.n-card__content) {
  padding: 16px;
}
</style>

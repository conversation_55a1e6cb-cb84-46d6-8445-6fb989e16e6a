<script setup lang="ts">
import { computed } from 'vue'
import { useThemeVars } from 'naive-ui'
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import type { ECOption } from '@/hooks/useEcharts'

const themeVars = useThemeVars()

const chartOptions = computed<ECOption>(() => {
  // 模拟数据 - 可以根据需要添加时间范围选择
  const times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
  const thisYear = [30, 25, 60, 80, 40, 70, 50]
  const lastYear = [45, 40, 35, 30, 50, 45, 60]

  // 定义颜色
  const thisYearColor = '#5aacfe' // 本年蓝色
  const lastYearColor = '#f3cc88' // 去年黄色

  return {
    color: [thisYearColor, lastYearColor],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      icon: 'rect',
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 15,
      data: ['本年', '去年同期'],
      right: '4%',
      textStyle: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false, // 折线图不需要边界间隔
      data: times,
      axisLabel: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '单位: kwh',
      nameTextStyle: {
        color: themeVars.value.textColor1,
      },
      axisLabel: {
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    series: [
      {
        name: '本年',
        type: 'line',
        data: thisYear,
        smooth: true, // 确保折线圆滑
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        lineStyle: {
          color: thisYearColor,
          width: 3, // 稍微加粗线条
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: `${thisYearColor}40` }, // 使用透明度
              { offset: 1, color: `${thisYearColor}10` },
            ],
          },
        },
        itemStyle: {
          color: thisYearColor,
          borderWidth: 2,
          borderColor: '#fff',
        },
        symbol: 'none',
      },
      {
        name: '去年同期',
        type: 'line',
        data: lastYear,
        smooth: true, // 确保折线圆滑
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        lineStyle: {
          color: lastYearColor,
          width: 3, // 稍微加粗线条
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: `${lastYearColor}40` }, // 使用透明度
              { offset: 1, color: `${lastYearColor}10` },
            ],
          },
        },
        itemStyle: {
          color: lastYearColor,
          borderWidth: 2,
          borderColor: '#fff',
        },
        symbol: 'none',
      },
    ],
  }
})

useEcharts('yearOnYearChart', chartOptions)
</script>

<template>
  <BCard title="用电量同比">
    <div ref="yearOnYearChart" class="w-full h-full" />
  </BCard>
</template>

<style scoped>
:deep(.n-card__content) {
  height: calc(100% - 50px);
}
</style>

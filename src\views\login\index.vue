<script setup lang="ts">
import { Login, Register, ResetPwd } from './components'

type IformType = 'login' | 'register' | 'resetPwd'
const formType: Ref<IformType> = ref('login')
const formComponets = {
  login: Login,
  register: Register,
  resetPwd: ResetPwd,
}
</script>

<template>
  <n-el class="wh-full flex-center" style="background-color: initial">
    <div class="fixed top-40px right-40px text-lg flex-col-center gap-3">
      <DarkModeSwitch />
      <LangsSwitch />
    </div>

    <n-el
      class="p-y-4xl p-x-xl h-full w-full sm:w-370px sm:h-unset fixed md:right-150px rounded-2 backdrop-blur-sm bg-white/30 dark:bg-black/30"
      style="box-shadow: var(--box-shadow-1);"
    >
      <div class="w-full flex flex-col items-center">
        <transition
          name="fade-slide"
          mode="out-in"
        >
          <component
            :is="formComponets[formType]"
            v-model="formType"
            class="w-85%"
          />
        </transition>
      </div>
    </n-el>

    <video
      src="/login-bg-video.mp4" autoplay muted loop
      class="absolute left-0 top-0 w-full h-full z--1"
    />
  </n-el>
</template>

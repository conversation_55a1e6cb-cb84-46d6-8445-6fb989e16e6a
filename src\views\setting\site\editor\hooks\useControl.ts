import { useVueFlow } from '@vue-flow/core'

export const topologyMapId = 'topologyMap'
const storageKey = 'vue-flow-topology-data'

export function useControl() {
  const { fitView, fromObject, toObject } = useVueFlow(topologyMapId)

  async function reset() {
    const savedData = localStorage.getItem(storageKey)
    if (savedData && await fromObject(JSON.parse(savedData))) {
      window.$message.success('恢复已恢复')
    }
    else {
      window.$message.error('恢复数据失败')
    }
  }

  function center() {
    return fitView({ padding: 0.1 })
  }

  function clear() {
    return fromObject({ nodes: [], edges: [] })
  }

  function save() {
    const data = JSON.stringify(toObject())
    localStorage.setItem(storageKey, data)
    window.$message.success('拓扑图已保存！')
  }

  return { center, clear, save, reset }
}

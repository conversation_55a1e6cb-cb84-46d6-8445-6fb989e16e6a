<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import type { ProDataTableColumns } from 'pro-naive-ui/es/data-table/types'
import { tableColumns } from './columns/tableColumns'
import { useSearchFormColumn } from './columns/searchFormColumn'
import EditModal from './components/EditModal.vue'
import type { CreateProModalFormReturn } from 'pro-naive-ui/es/modal-form/composables/create-pro-modal-form'
import CreateModal from './components/CreateModal.vue'
import { delDevice, exportDevice, listDevice } from '@/service/api/setting/device'
import { useDialog } from 'naive-ui'
import { useDict } from '@/hooks'
import { BatchDeleteButton, CreateButton, DeleteButton, EditButton, ExportButton } from '@/components/curd/Button'
import type { UseNDataTableParams } from 'pro-naive-ui/es/composables/use-n-data-table'

const form = createProSearchForm<Partial<Entity.Device>>({
  initialValues: {},
})

const dialog = useDialog()
const editModalRef = ref<CreateProModalFormReturn>()
const createModalRef = ref<CreateProModalFormReturn>()

// 选中行状态
const selectedRowKeys = ref<Entity.Device['id'][]>([])

// 获取设备类型字典数据
const { options: deviceTypeOptions } = useDict('device_type')
// 根据字典值获取标签
function getDeviceTypeLabel(value: string) {
  const option = deviceTypeOptions.value?.find(item => item.key === value)
  return option?.label || value
}

// 动态生成表格列配置
const dynamicTableColumns = computed(() => {
  return tableColumns.map((column) => {
    if ('key' in column && column.key === 'deviceType') {
      return {
        ...column,
        render(row: Entity.Device) {
          return getDeviceTypeLabel(row.deviceType)
        },
      }
    }
    return column
  })
})

// 动态生成搜索表单列配置
const searchFormColumn = useSearchFormColumn()

const {
  table: { tableProps },
  search: {
    proSearchFormProps,
  },
  refresh,
} = useNDataTable(fetchList, { form })

async function fetchList(params: UseNDataTableParams, formData: Record<string, any>) {
  try {
    const { current, pageSize, ...rest } = params
    const response = await listDevice({
      pageNum: current,
      pageSize,
      ...rest,
      ...formData,
    })

    return {
      total: response.total,
      list: response.rows || [],
    }
  }
  catch (error) {
    console.error('获取设备列表失败:', error)
    window.$message.error('获取设备列表失败')
    return {
      total: 0,
      list: [],
    }
  }
}

// 操作列
const ActionColumn: ProDataTableColumns<Entity.Device>[number] = {
  title: '操作',
  fixed: 'right',
  width: 120,
  render(row) {
    return (
      <n-space>
        <EditButton onClick={() => onEdit(row)} />
        <DeleteButton type="error" onClick={() => onRemove(row.deviceId)} />
      </n-space>
    )
  },
}

function onCreate() {
  createModalRef.value?.open()
}

// 批量删除
function onBatchDelete() {
  const deviceNames = tableProps.value.data.filter(e => toValue(selectedRowKeys).includes(e.id)).map(site => site.deviceName).join('、')
  dialog.warning({
    title: '批量删除确认',
    content: `你确定删除以下设备吗？\n${deviceNames}`,
    positiveText: '确定删除',
    negativeText: '取消',
    draggable: true,
    onPositiveClick: async () => {
      await delDevice(toValue(selectedRowKeys).join(','))
      window.$message.success('批量删除成功')
      // 清空选择
      selectedRowKeys.value = []
      // 刷新表格
      refresh()
    },
  })
}

// 导出功能
async function onExport() {
  const params = {
    ...form.fieldsValue.value,
  }
  await exportDevice(params)
  window.$message.success('导出成功')
}

async function onRemove(deviceId: string) {
  dialog.warning({
    title: '提示',
    content: '你确定删除该设备？',
    positiveText: '确定',
    negativeText: '取消',
    draggable: true,
    onPositiveClick: async () => {
      await delDevice(deviceId)
      window.$message.success('删除成功')
      // 刷新表格
      refresh()
    },
  })
}

function onEdit(device: Entity.Device) {
  if (editModalRef.value) {
    editModalRef.value.setInitialValues(toRaw(device))
    editModalRef.value.resetFieldsValue()
    editModalRef.value.open()
  }
  else {
    console.error('editModalRef 未定义')
  }
}

// 监听模态框关闭事件，刷新表格
function onModalClose() {
  refresh()
}
</script>

<template>
  <div class="h-full p-3">
    <pro-search-form
      :form="form"
      :columns="searchFormColumn"
      v-bind="proSearchFormProps"
    />
    <pro-data-table
      v-bind="tableProps"
      v-model:checked-row-keys="selectedRowKeys"
      :columns="[...dynamicTableColumns, ActionColumn]"
      :row-key="(row) => row.deviceId"
    >
      <template #title>
        <CreateButton @click="onCreate">
          添加设备
        </CreateButton>
      </template>
      <template #toolbar>
        <n-space class="gap-2">
          <BatchDeleteButton
            :disabled="selectedRowKeys.length === 0"
            @click="onBatchDelete()"
          >
            {{ selectedRowKeys.length > 0 ? `删除(${selectedRowKeys.length})` : '删除' }}
          </BatchDeleteButton>
          <ExportButton @click="onExport" />
        </n-space>
      </template>
    </pro-data-table>
    <EditModal ref="editModalRef" @close="onModalClose" />
    <CreateModal ref="createModalRef" @close="onModalClose" />
  </div>
</template>

<style scoped>

</style>

<script setup lang="ts">
import type { ECOption } from '@/hooks'
import { useEcharts } from '@/hooks'

// 模拟报警数据
const alarmData = [
  { value: 135, name: '一级预警', color: '#54CE8E' },
  { value: 40, name: '二级预警', color: '#FFC371' },
  { value: 25, name: '三级预警', color: '#F23F20' },
]

// 计算总报警数
const totalAlarms = computed(() => alarmData.reduce((sum, item) => sum + item.value, 0))

// 设备名称
const deviceName = ref('逆变器')

const chartOptions = computed<ECOption>(() => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.marker}${params.name}: ${params.value}次 (${params.percent}%)`
      },
    },
    legend: {
      orient: 'vertical',
      bottom: '5%',
      right: 'right',
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        fontSize: 12,
        color: '#666',
      },
    },
    series: [
      {
        name: '报警占比',
        type: 'pie',
        label: {
          show: true,
          position: 'outside',
          formatter: (params: any) => {
            return `${params.name}\n${params.percent}%（${params.value}个）`
          },
          color: 'inherit',
          fontSize: 12,
          borderWidth: 0,
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 20,
        },
        data: alarmData.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
          },
        })),
      },
    ],
  }
})

useEcharts('pieChart', chartOptions)
</script>

<template>
  <n-card :bordered="false">
    <div class="h-full flex flex-col">
      <!-- 饼图区域 -->
      <div ref="pieChart" class="flex-1 min-h-[200px]" />

      <!-- 底部信息区域 -->
      <n-el class="flex">
        <div class="w-130px flex-center p-2 bg-[var(--primary-color)] c-[var(--base-color)]"
        
        >
          报警总数: {{ totalAlarms }}
        </div>
        <div class="flex-1 p-2 flex-center bg-[var(--base-color)] c-[var(--text-color-base)]">
          {{ deviceName }}
        </div>
      </n-el>
    </div>
  </n-card>
</template>

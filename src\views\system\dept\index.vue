<script setup lang="tsx">
import { createProSearchForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { delDept, listDept } from '@/service'
import { arrayToTree } from '@/utils'
import DeptModal from './components/DeptModal.vue'
import { createTableColumns, searchColumns } from './columns'
import type { SearchFormData } from './columns'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

async function deleteData(deptId: number) {
  window.$dialog.warning({
    title: '确认删除',
    content: '是否确认删除该部门？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await delDept(deptId)
        window.$message.success('删除成功')
        await getAllDepts() // 刷新列表
      }
      catch (error) {
        console.error('删除部门失败:', error)
        window.$message.error('删除失败')
      }
    },
  })
}

const deptModalRef = ref()

// 使用抽离的表格列配置
const tableColumns = createTableColumns({
  onEdit: (row: Entity.Dept) => {
    deptModalRef.value.openModal('edit', row)
  },
  onDelete: (deptId: number) => {
    deleteData(deptId)
  },
})

const tableData = ref<Entity.Dept[]>([])

async function getAllDepts(params?: SearchFormData) {
  try {
    startLoading()
    const { data } = await listDept(params)
    // 删除已有的 children 字段，避免与 arrayToTree 冲突
    const cleanDeptList = data.map((dept) => {
      const { children, ...cleanDept } = dept
      return cleanDept
    })
    // 构建树形结构
    tableData.value = arrayToTree(cleanDeptList, 'deptId', 'parentId') as Entity.Dept[]
  }
  catch {
    window.$message.error('获取部门列表失败')
    tableData.value = []
  }
  finally {
    endLoading()
  }
}

// 使用抽离的搜索表单配置
const searchForm = createProSearchForm<SearchFormData>({
  defaultCollapsed: true,
  onSubmit: getAllDepts,
  onReset: getAllDepts,
})

onMounted(() => {
  getAllDepts()
})
</script>

<template>
  <div>
    <pro-search-form
      :form="searchForm"
      :columns="searchColumns"
      :collapse-button-props="false"
    />
    <pro-data-table
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      row-key="deptId"
    >
      <template #title>
        <NButton type="primary" @click="deptModalRef.openModal('add')">
          <template #icon>
            <icon-park-outline-plus />
          </template>
          新建
        </NButton>
      </template>
    </pro-data-table>
    <DeptModal ref="deptModalRef" modal-name="部门" @success="getAllDepts" />
  </div>
</template>

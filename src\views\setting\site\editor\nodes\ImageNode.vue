<script setup lang="ts">
import { <PERSON><PERSON>, Position } from '@vue-flow/core'
import type { MetaData } from '@/views/setting/site/editor/types/NodeData'

defineOptions({
  inheritAttrs: false,
})

defineProps<{
  image: string
  meta: MetaData
}>()
</script>

<template>
  <img class="h-[51px]" :src="image" alt="icon">
  <n-text>{{ meta.name }}</n-text>
  <Handle type="target" :position="Position.Top" />
  <Handle type="source" :position="Position.Bottom" />
</template>

<style scoped>

</style>

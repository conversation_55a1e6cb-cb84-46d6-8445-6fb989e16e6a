import type { FormRules } from 'naive-ui'
import { NForm, NFormItem, NInput } from 'naive-ui'

// 类型定义
export interface FormItemConfig {
  path: string
  label: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
}

export interface SimpleFormProps {
  items: FormItemConfig[]
  model: Record<string, any>
  rules?: FormRules
  labelWidth?: string | number
  labelAlign?: 'left' | 'right'
  labelPlacement?: 'top' | 'left'
}

// 工厂函数实现
export function createSimpleFormFactory(config: SimpleFormProps) {
  // 直接返回渲染好的表单
  return (
    <NForm
      model={config.model}
      rules={config.rules}
      labelWidth={config.labelWidth}
      labelAlign={config.labelAlign}
      labelPlacement={config.labelPlacement}
    >
      {config.items.map(item => (
        <NFormItem
          key={item.path}
          path={item.path}
          label={item.label}
          rule={{ required: item.required, ...config.rules?.[item.path] }}
        >
          <NInput
            v-model:value={config.model[item.path]}
            placeholder={item.placeholder}
            disabled={item.disabled}
          />
        </NFormItem>
      ))}
    </NForm>
  )
}

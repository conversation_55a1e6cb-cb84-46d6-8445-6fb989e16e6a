<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import type { ProDataTableColumns } from 'pro-naive-ui/es/data-table/types'
import { tableColumns } from './columns/tableColumns'
import { useSearchFormColumn } from './columns/searchFormColumn'
import { useDialog } from 'naive-ui'
import { delMaintenance, exportMaintenance, listMaintenance } from '@/service/api/setting/maintenance'
import type { CreateProModalFormReturn } from 'pro-naive-ui/es/modal-form/composables/create-pro-modal-form'
import CreateModal from './components/CreateModal.vue'
import EditModal from './components/EditModal.vue'
import { BatchDeleteButton, CreateButton, DeleteButton, EditButton, ExportButton } from '@/components/curd/Button'
import type { UseNDataTableParams } from 'pro-naive-ui/es/composables/use-n-data-table'

const form = createProSearchForm<Partial<Entity.Maintenance>>({
  initialValues: {
    deviceName: '',
    producer: '',
    deviceModel: '',
  },
})

const dialog = useDialog()
const createModalRef = ref<CreateProModalFormReturn>()
const editModalRef = ref<CreateProModalFormReturn>()

// 选中行状态
const selectedRowKeys = ref<string[]>([])

// 动态生成搜索表单列配置
const searchFormColumn = useSearchFormColumn()

const {
  table: { tableProps },
  search: {
    proSearchFormProps,
  },
  refresh,
} = useNDataTable(fetchList, { form })

async function fetchList(params: UseNDataTableParams, formData: Record<string, any>) {
  try {
    const { current, pageSize, ..._rest } = params
    const response = await listMaintenance({
      pageNum: current,
      pageSize,
      ..._rest,
      ...formData,
    })

    return {
      total: response.total,
      list: response.rows || [],
    }
  }
  catch (error) {
    console.error('获取设备维护列表失败:', error)
    window.$message.error('获取设备维护列表失败')
    return {
      total: 0,
      list: [],
    }
  }
}

// 操作列
const ActionColumn: ProDataTableColumns<Entity.Maintenance>[number] = {
  title: '操作',
  fixed: 'right',
  width: 120,
  render(row) {
    return (
      <n-space>
        <EditButton onClick={() => onEdit(row)} />
        <DeleteButton onClick={() => onRemove(row.id)} />
      </n-space>
    )
  },
}

function onCreate() {
  createModalRef.value?.open()
}

// 批量删除
function onBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    dialog.warning({
      title: '提示',
      content: '请先选择要删除的设备维护记录',
      positiveText: '确定',
    })
    return
  }

  const maintenanceRecords = tableProps.value.data.filter(e => toValue(selectedRowKeys).includes(e.id.toString())).map(record => record.deviceName || '未命名设备').join('、')
  dialog.warning({
    title: '批量删除确认',
    content: `你确定删除以下设备维护记录吗？\n${maintenanceRecords}`,
    positiveText: '确定删除',
    negativeText: '取消',
    draggable: true,
    onPositiveClick: async () => {
      const ids = toValue(selectedRowKeys).join(',')
      await delMaintenance(ids)
      window.$message.success('批量删除成功')
      // 清空选择
      selectedRowKeys.value = []
      // 刷新表格
      refresh()
    },
  })
}

// 导出功能
async function onExport() {
  const params = {
    ...form.fieldsValue.value,
  }
  await exportMaintenance(params)
  window.$message.success('导出成功')
}

async function onRemove(id: number) {
  dialog.warning({
    title: '提示',
    content: '你确定删除该设备维护记录？',
    positiveText: '确定',
    negativeText: '取消',
    draggable: true,
    onPositiveClick: async () => {
      await delMaintenance(id.toString())
      window.$message.success('删除成功')
      // 刷新表格
      refresh()
    },
  })
}

function onEdit(maintenance: Entity.Maintenance) {
  if (editModalRef.value) {
    editModalRef.value.setInitialValues(toRaw(maintenance))
    editModalRef.value.resetFieldsValue()
    editModalRef.value.open()
  }
  else {
    console.error('editModalRef 未定义')
  }
}

// 监听模态框关闭事件，刷新表格
function onModalClose() {
  refresh()
}
</script>

<template>
  <div class="h-full p-3">
    <pro-search-form
      :form="form"
      :columns="searchFormColumn"
      :collapse-button-props="false"
      v-bind="proSearchFormProps"
    />

    <pro-data-table
      v-bind="tableProps"
      v-model:checked-row-keys="selectedRowKeys"
      :columns="[...tableColumns, ActionColumn]"
      :pagination="{ ...tableProps.pagination, showSizePicker: true }"
      :row-key="(row) => row.id"
    >
      <template #title>
        <CreateButton @click="onCreate">
          新增设备维护
        </CreateButton>
      </template>
      <template #toolbar>
        <n-space class="gap-2">
          <BatchDeleteButton
            :disabled="selectedRowKeys.length === 0"
            @click="onBatchDelete"
          >
            {{ selectedRowKeys.length > 0 ? `删除(${selectedRowKeys.length})` : '删除' }}
          </BatchDeleteButton>
          <ExportButton @click="onExport">
            导出
          </ExportButton>
        </n-space>
      </template>
    </pro-data-table>
    <CreateModal ref="createModalRef" @close="onModalClose" />
    <EditModal ref="editModalRef" @close="onModalClose" />
  </div>
</template>

<style scoped>

</style>

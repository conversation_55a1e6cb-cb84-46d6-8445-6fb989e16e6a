<script setup lang="tsx">
import { createProSearchForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { delMenu, listMenu, updateMenu } from '@/service'
import { arrayToTree } from '@/utils'
import TableModal from './components/TableModal.vue'
import { createTableColumns, searchColumns } from './columns'
import type { SearchFormData } from './columns'

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

/** 删除菜单 */
async function deleteData(menuId: number) {
  window.$dialog.warning({
    title: '确认删除',
    content: '是否确认删除该菜单？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await delMenu(menuId.toString())
        window.$message.success('删除成功')
        await getAllRoutes() // 刷新列表
      }
      catch (error) {
        console.error('删除菜单失败:', error)
      }
    },
  })
}

/** 处理状态切换 */
async function handleStatusChange(row: Entity.Menu, value: '0' | '1') {
  const updatedMenu = { ...row, status: value }

  try {
    await updateMenu(updatedMenu)

    // 更新本地数据
    row.status = value
    window.$message.success(value ? '菜单已启用' : '菜单已停用')
  }
  catch (error) {
    console.error('更新菜单状态失败:', error)
    window.$message.error('状态更新失败')

    await getAllRoutes()
  }
}

const tableModalRef = ref()

// 新建子菜单处理函数
function handleAdd(row: Entity.Menu) {
  tableModalRef.value.openModal('add', row)
}

// 使用抽离的表格列配置
const tableColumns = createTableColumns({
  onEdit: row => tableModalRef.value.openModal('edit', row),
  onDelete: deleteData,
  onStatusChange: handleStatusChange,
  onAdd: handleAdd,
})

const tableData = ref<Entity.Menu[]>([])

async function getAllRoutes(params?: SearchFormData) {
  try {
    startLoading()
    const { data } = await listMenu(params)
    // 直接使用后端数据结构，构建树形结构
    tableData.value = arrayToTree(data, 'menuId', 'parentId')
  }
  catch {
    window.$message.error('获取路由列表失败')
    tableData.value = []
  }
  finally {
    endLoading()
  }
}

// 使用抽离的搜索表单配置
const searchForm = createProSearchForm<SearchFormData>({
  onSubmit: getAllRoutes,
  onReset: getAllRoutes,
})

onMounted(() => {
  getAllRoutes()
})
</script>

<template>
  <div>
    <pro-search-form :form="searchForm" :columns="searchColumns" :collapse-button-props="false" />

    <pro-data-table :data="tableData" :columns="tableColumns" :loading="loading" row-key="menuId">
      <template #title>
        <n-space>
          <n-button
            v-hasPermi="'system:menu:add'"
            type="primary"
            @click="tableModalRef.openModal('add')"
          >
            <template #icon>
              <icon-park-outline-plus />
            </template>
            新建
          </n-button>
        </n-space>
      </template>
    </pro-data-table>

    <TableModal ref="tableModalRef" modal-name="菜单" @success="getAllRoutes" />
  </div>
</template>

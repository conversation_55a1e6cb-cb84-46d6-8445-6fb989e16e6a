<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { addDept, getDept, listDept, listDeptExcludeChild, updateDept } from '@/service'
import { arrayToTree } from '@/utils'

const props = defineProps<{
  modalName: string
}>()

const emit = defineEmits<{
  success: []
}>()

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// 部门树数据
const deptTreeData = ref<Entity.Dept[]>([])

const modalForm = createProModalForm<Partial<Entity.Dept>>({
  omitEmptyString: false,
  initialValues: {
    parentId: 0,
    deptName: '',
    orderNum: 0,
    leader: null,
    phone: null,
    email: null,
    status: '0',
  },
  onSubmit: handleSubmit,
})

type ModalType = 'add' | 'edit'
const modalType = shallowRef<ModalType>('add')
const modalTitle = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '添加',
    edit: '编辑',
  }
  return `${titleMap[modalType.value]}${props.modalName}`
})

// 获取部门树数据
async function getDeptTreeData(excludeDeptId?: number) {
  try {
    let response
    if (excludeDeptId) {
      // 编辑时排除当前部门
      response = await listDeptExcludeChild(excludeDeptId)
    }
    else {
      // 新增时获取所有部门
      response = await listDept()
    }

    const deptList = response.data || []
    // 删除已有的 children 字段
    const cleanDeptList = deptList.map((dept) => {
      const { children, ...cleanDept } = dept
      return cleanDept
    })
    // 构建树形结构
    deptTreeData.value = arrayToTree(cleanDeptList, 'deptId', 'parentId') as Entity.Dept[]
  }
  catch (error) {
    console.error('获取部门树失败:', error)
    deptTreeData.value = []
  }
}

// 打开弹窗
async function openModal(type: ModalType, data?: Entity.Dept) {
  modalType.value = type
  modalForm.open()

  const handlers = {
    async add() {
      // 获取部门树数据（新增时不排除任何部门）
      await getDeptTreeData()
    },
    async edit() {
      if (!data)
        return
      // 获取完整的部门信息
      const response = await getDept(data.deptId)
      modalForm.values.value = response.data
      // 获取部门树数据（编辑时排除当前部门）
      await getDeptTreeData(data.deptId)
    },
  }
  await handlers[type]()
}

// 提交表单
async function handleSubmit(filedValues: Partial<Entity.Dept>) {
  try {
    startLoading()

    if (modalType.value === 'add') {
      await addDept(filedValues)
      window.$message.success('添加成功')
    }
    else {
      await updateDept({
        ...filedValues,
        deptId: modalForm.values.value.deptId,
      })
      window.$message.success('修改成功')
    }

    modalForm.close()
    emit('success')
  }
  catch (error) {
    console.error('提交失败:', error)
    window.$message.error('提交失败')
  }
  finally {
    endLoading()
  }
}

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :title="modalTitle"
    :loading="loading"
    width="700px"
  >
    <n-grid cols="2" x-gap="16">
      <n-gi span="2">
        <pro-tree-select
          title="上级部门"
          path="parentId"
          required
          :field-props="{
            clearable: true,
            filterable: true,
            options: deptTreeData,
            keyField: 'deptId',
            labelField: 'deptName',
            defaultExpandAll: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-input title="部门名称" path="deptName" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-digit title="显示排序" path="orderNum" required />
      </n-gi>

      <n-gi>
        <pro-input title="负责人" path="leader" />
      </n-gi>

      <n-gi>
        <pro-input title="联系电话" path="phone" />
      </n-gi>

      <n-gi>
        <pro-input title="联系电话" path="email" />
      </n-gi>

      <n-gi>
        <pro-radio-group
          title="部门状态"
          path="status"
          :field-props="{
            options: [
              {
                label: '正常',
                value: '0',
              },
              {
                label: '停用',
                value: '1',
              },
            ],
          }"
        />
      </n-gi>
    </n-grid>
  </pro-modal-form>
</template>

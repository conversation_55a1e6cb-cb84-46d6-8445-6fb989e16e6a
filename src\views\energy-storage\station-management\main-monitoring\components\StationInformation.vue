<script setup lang="tsx">
import BCard from '@/components/big-screen/BCard.vue'
import type { FunctionalComponent } from 'vue'

import PowerStationPower from '@/assets/imgs/energy-storage/station-management/power-station-power.png'
import PowerStationCapacity from '@/assets/imgs/energy-storage/station-management/power-station-capacity.png'
import InstalledCapacity from '@/assets/imgs/energy-storage/station-management/installed-capacity.png'
import InstalledPower from '@/assets/imgs/energy-storage/station-management/installed-power.png'
import DialogBackground from '@/assets/imgs/energy-storage/station-management/dialog-background.png'

const dialog = useDialog()

const Content: FunctionalComponent = () => {
  return (
    <div>
      <header class="flex justify-between px-5 py-3">
        <div class="flex whitespace-nowrap gap-3 items-center">
          <img src={InstalledPower} alt="icon" />
          <n-text depth="1">装机功率</n-text>
          <n-text depth="2" strong>XXXX</n-text>
          <n-text depth="3">KW</n-text>
        </div>
        <div class="flex whitespace-nowrap gap-3 items-center">
          <img src={InstalledCapacity} alt="icon" />
          <n-text depth="1">装机容量</n-text>
          <n-text depth="2" strong>XXXX</n-text>
          <n-text depth="3">KWh</n-text>
        </div>
      </header>
      <main>
        <img class="h-[330px]" src="https://dummyimage.com/600x330/000/fff" alt="效果图" />
      </main>
    </div>
  )
}

function onClick() {
  dialog.create({
    title: () => <n-text class="ml-7" type="primary">电站详情</n-text>,
    content: Content,
    showIcon: false,
    closable: false,
    style: {
      width: '600px',
      backgroundImage: `url(${DialogBackground})`,
      backgroundSize: '100% 100%',
    },
  })
}
</script>

<template>
  <BCard title="电站信息" class="powerStationInformation">
    <template #extend>
      <n-button text @click="onClick()">
        查看详情
      </n-button>
    </template>
    <div class="flex justify-between items-center px-5">
      <BIncomeItem reverse title="电站容量(MWh)" :icon="PowerStationCapacity" value="xxx" />
      <BIncomeItem reverse title="电站功率(MWh)" :icon="PowerStationPower" value="xxx" />
    </div>
  </BCard>
</template>

<style scoped>

</style>

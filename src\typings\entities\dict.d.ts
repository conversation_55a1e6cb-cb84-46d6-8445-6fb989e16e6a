/// <reference path="../global.d.ts"/>

/* 字典数据库表字段 */
namespace Entity {

  interface Dict {
    id?: number
    isRoot?: 0 | 1
    code: string
    label: string
    value?: number
  }

  // 字典类型
  interface DictType {
    dictId: number
    dictName: string
    dictType: string
    status: '0' | '1'
    createBy?: string
    createTime?: string
    updateBy?: string
    updateTime?: string
    remark?: string
  }

  // 字典数据
  interface DictData {
    dictCode?: number
    dictSort?: number
    dictLabel: string
    dictValue: string
    dictType: string
    cssClass?: string
    listClass?: string
    isDefault?: 'Y' | 'N'
    status: '0' | '1'
    createBy?: string
    createTime?: string
    updateBy?: string
    updateTime?: string
    remark?: string
  }
}

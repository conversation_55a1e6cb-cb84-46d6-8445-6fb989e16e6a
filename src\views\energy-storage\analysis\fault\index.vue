<script setup lang="ts">
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
</script>

<template>
  <EnergyStorageBackground class="h-full">
    <BHeader title="分布式储能运营云平台" />
    <main class=" h-[calc(100%-80px)] grid grid-rows-[auto_1fr] gap-3 p-3">
      <div class="flex gap-3">
        <n-select class="w-300px" />
        <n-date-picker type="daterange" class="ml-auto" />
        <n-button type="primary">
          查询
        </n-button>
      </div>

      <BCard title="设备故障分析">
        <div class="grid grid-cols-3">
          <div v-for="value in 10" :key="value" class="h-400px">
            {{ value }}
          </div>
        </div>
      </BCard>
    </main>
  </EnergyStorageBackground>
</template>

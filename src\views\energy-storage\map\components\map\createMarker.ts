import Point from '@/assets/imgs/energy-storage/map/point.png'
import { primaryCommon } from '@/store/app/customTheme'
import { random } from 'radash'

interface Site {
  name: string
  position: [number, number]
}

const textStyle: AMap.TextStyleOptions = {
  // backgroundColor: '#f5a54f'
  fillColor: primaryCommon?.primaryColor,
  strokeColor: '#fff',
  strokeWidth: 2,
  fold: true,
  padding: '2, 5',
  fontSize: 12,
  fontWeight: 'normal',
}

const icon: AMap.LabelMarkerIconOptions = {
  // 图标类型，现阶段只支持 image 类型
  // type: 'image',
  // 图片 url
  image: Point,
  // 图片尺寸
  size: [63, 88],
  // 图片相对 position 的锚点，默认为 bottom-center
  anchor: 'center',
}

// function onLabelClick(e: any) {
//   const site: Site | undefined = e?.target?._originOpts?.extData
//
//   console.log("click!", toRaw(site));
// }

export function createMarker(site: Site) {
  const labelMarker = new AMap.LabelMarker({
    name: `场站:${site.name}`,
    position: site.position,
    zooms: [6, 20], // 显示的地图层级
    opacity: 1,
    zIndex: 10,
    icon,
    text: {
      offset: [-63, 50], // 负值 = 向上；高度 = icon高度 + 间距
      content: site.name,
      direction: 'right',
      style: textStyle,
    },
    extData: site,
  })

  // labelMarker.on('click', onLabelClick)

  return labelMarker
}

export function randomMarker() {
  return createMarker({
    position: [117.227239 + random(-100, 100) / 100, 31.820586 + random(-100, 100) / 100],
    name: `场站:${random(1, 1e3)}`,
  })
}

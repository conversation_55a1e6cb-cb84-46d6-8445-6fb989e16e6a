<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { ref } from 'vue'
import { addCardNumber } from '@/service/api/setting/card'
import type { CardNumberInfo } from '@/service/api/setting/card'

const emit = defineEmits<{
  close: []
}>()
const loading = ref(false)

// 使用样例数据
const siteOptions = ref<Array<{ label: string, value: number }>>([
  { label: '站点1', value: 1 },
  { label: '站点2', value: 2 },
  { label: '站点3', value: 3 },
  { label: '站点4', value: 4 },
  { label: '站点5', value: 5 },
])

const modalForm = createProModalForm({
  onSubmit: async (values) => {
    loading.value = true
    try {
      await addCardNumber(values as Omit<CardNumberInfo, 'id'>)
      window.$message.success('新增卡号成功')
      modalForm.close()
      emit('close')
    }
    finally {
      loading.value = false
    }
  },
})

defineExpose(modalForm)
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :loading="loading"
    title="新增卡号"
    label-width="120"
    width="800px"
  >
    <div class="grid grid-cols-2">
      <ProInput title="卡号" path="cardNumber" required />
      <ProSelect
        title="所属站点"
        path="siteId"
        :field-props="{ options: siteOptions }"
        required
      />
      <ProDate
        title="开户时间"
        path="accountOpeningTime"
        :field-props="{ type: 'datetime' }"
        required
      />
      <ProDate
        title="服务开始时间"
        path="serviceStartTime"
        :field-props="{ type: 'datetime' }"
        required
      />
      <ProDate
        title="服务结束时间"
        path="serviceStopTime"
        :field-props="{ type: 'datetime' }"
        required
      />
      <ProInput title="周期套餐流量" path="periodicPackageData" required />
      <ProInput title="已使用流量" path="usedData" required />
      <ProTextarea :rows="3" title="备注" path="remark" class="col-span-2" />
    </div>
  </pro-modal-form>
</template>

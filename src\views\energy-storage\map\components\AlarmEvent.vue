<script setup lang="ts">
import BCard from '@/components/big-screen/BCard.vue'
import Decline from '@/assets/imgs/energy-storage/map/decline.png'
import NumberItem from './common/NumberItem.vue'
import { useEchartsOptions } from '../echarts-options/AlarmEvent'
import { useEcharts } from '@/hooks'

const detectingAccidentsAndIncidents = ref(304000)

useEcharts('ordinary', useEchartsOptions({ name: '普通', color: '#1b89f0', value: 60 }))
useEcharts('serious', useEchartsOptions({ name: '普通', color: '#ffa10a', value: 70 }))
useEcharts('accident', useEchartsOptions({ name: '普通', color: '#f33f21', value: 80 }))
</script>

<template>
  <BCard class="h-[287px]" title="告警事件">
    <div class="px-5">
      <n-p>检测事故事件</n-p>
      <n-space align="end" justify="space-between">
        <n-space :size="5" align="end">
          <NumberItem v-for="item in detectingAccidentsAndIncidents.toString()" :key="item" :value="Number(item)" />
          <n-text>起</n-text>
        </n-space>
        <div class="flex gap-2 items-center">
          <n-text>同比</n-text>
          <n-text style="color: var(--error-color)">
            0.24%
          </n-text>
          <img class="h-[16px] ml--3px mt--3px" :src="Decline" alt="icon">
        </div>
      </n-space>
    </div>
    <div class="flex h-[160px] gap-2 items-center justify-between">
      <div ref="ordinary" class="h-full w-full" />
      <div ref="serious" class="h-full w-full" />
      <div ref="accident" class="h-full w-full" />
    </div>
  </BCard>
</template>

<style scoped>

</style>

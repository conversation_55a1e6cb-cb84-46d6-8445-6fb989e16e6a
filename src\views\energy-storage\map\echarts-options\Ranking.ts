import { useThemeVars } from 'naive-ui'
import type { ECOption } from '@/hooks'

const data = [{
  name: '场站名',
  value: 1864,
}, {
  name: '场站名',
  value: 2543,
}, {
  name: '场站名',
  value: 3521,
}, {
  name: '场站名',
  value: 3546,
}, {
  name: '场站名',
  value: 4895,
}, {
  name: '场站名',
  value: 6545,
}, {
  name: '场站名',
  value: 8398,
}, {
  name: '场站名',
  value: 10352,
}]// 已排序好的数组

const ydata: Array<string> = []
for (let i = 0; i < data.length; i++) {
  ydata.push(data[i].name)
}
const datalength: Array<number> = []
for (let i = 0; i < data.length; i++) {
  datalength.push(0)
}
const databg: Array<number> = []
for (let i = 0; i < data.length; i++) {
  databg.push(data[data.length - 1].value)
}

export function useEchartsOptions() {
  const themeVars = useThemeVars()

  return computed(() => {
    return {
      // backgroundColor: '#fff',
      grid: {
        left: '5%',
        top: '2%',
        right: '5%',
        bottom: '2%',
        containLabel: true,
      },
      xAxis: [{
        show: false,
      }],
      yAxis: [
        {
          axisTick: 'none',
          axisLine: 'none',
          offset: '10',
          axisLabel: {
            formatter(value: any, index: number) {
              return `No.${index + 1}  ${value}`
            },
            textStyle: {
              color: themeVars.value.textColor2,
              fontSize: '14',
              fontWeight: 'bold',
            },
          },
          data: ydata,
        },
        {
          axisTick: 'none',
          axisLine: 'none',
          axisLabel: {
            textStyle: {
              color: themeVars.value.textColor2,
              fontSize: '16',
              fontWeight: 'bold',
            },
          },
          data,
        },
        {
          axisLine: {
            lineStyle: {
              color: 'rgba(0,0,0,0)',
            },
          },
          data: [],
        },
      ],
      series: [{
        name: '条',
        type: 'bar',
        stack: '圆',
        yAxisIndex: 0,
        data,
        barWidth: 5,
        itemStyle: {
          normal: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#0075FF', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#0075FF', // 100% 处的颜色
                },
              ],
            },
            barBorderRadius: 0,
          },
        },
        z: 2,
      }, {
        name: '内圆',
        type: 'scatter',
        stack: '圆',
        yAxisIndex: 0,
        data: datalength,
        label: false,
        symbolSize: 8,
        itemStyle: {
          normal: {
            borderColor: themeVars.value.primaryColor,
            borderWidth: 4,
            color: themeVars.value.bodyColor,
            opacity: 1,
          },
        },
        z: 2,
      }, {
        name: '内圆框',
        type: 'scatter',
        stack: '圆',
        yAxisIndex: 0,
        data: datalength,
        label: false,
        symbolSize: 20,
        itemStyle: {
          normal: {
            borderColor: themeVars.value.borderColor,
            borderWidth: 1,
            color: themeVars.value.primaryColor,
            opacity: 0.2,
          },
        },
        z: 1,
      }, {
        name: '白框',
        type: 'bar',
        yAxisIndex: 1,
        barGap: '-100%',
        data: databg,
        barWidth: 4,
        itemStyle: {
          normal: {
            color: themeVars.value.bodyColor,
            barBorderRadius: 5,
          },
        },
        z: 0,
      }],
    } as unknown as ECOption
  })
}

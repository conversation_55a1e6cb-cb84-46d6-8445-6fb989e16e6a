import { request } from '../../http'

// 字典数据查询参数
export interface DictDataQueryParams {
  dictType?: string
  dictValue?: string
  status?: '0' | '1'
  pageNum?: number
  pageSize?: number
}

// 查询字典数据列表
export function listData(params?: DictDataQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.DictData[]>>('/system/dict/data/list', { params })
}

// 查询字典数据详细
export function getData(dictCode: number) {
  return request.Get<Api.ResponseWithData<Entity.DictData>>(`/system/dict/data/${dictCode}`)
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType: string) {
  return request.Get<Api.ResponseWithData<Entity.DictData[]>>(`/system/dict/data/type/${dictType}`)
}

// 新增字典数据
export function addData(data: Partial<Entity.DictData>) {
  return request.Post<Api.BaseResponse>('/system/dict/data', data)
}

// 修改字典数据
export function updateData(data: Partial<Entity.DictData>) {
  return request.Put<Api.BaseResponse>('/system/dict/data', data)
}

// 删除字典数据
export function delData(dictCodes: string) {
  return request.Delete<Api.BaseResponse>(`/system/dict/data/${dictCodes}`)
}

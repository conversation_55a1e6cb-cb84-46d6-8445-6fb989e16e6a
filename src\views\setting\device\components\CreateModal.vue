<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { ref } from 'vue'
import ModalContent from './ModalContent.vue'
import { addDevice } from '@/service/api/setting/device'
import type { DeviceInfo } from '@/service/api/setting/device'

const emit = defineEmits<{
  close: []
}>()
const loading = ref(false)

const modalForm = createProModalForm({
  onSubmit: async (values) => {
    loading.value = true
    try {
      await addDevice(values as Omit<DeviceInfo, 'deviceId' | 'createTime' | 'updateTime'>)
      window.$message.success('添加设备成功')
      modalForm.close()
      emit('close')
    }
    finally {
      loading.value = false
    }
  },
})

defineExpose(modalForm)
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :loading="loading"
    title="添加设备"
    width="1062px"
  >
    <ModalContent />
  </pro-modal-form>
</template>

<style scoped>

</style>

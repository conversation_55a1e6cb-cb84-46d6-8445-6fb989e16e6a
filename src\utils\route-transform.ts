/**
 * 后端路由数据结构
 */
interface BackendRoute {
  createBy?: string | null
  createTime?: string
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  dataScope?: string | null
  menuId: number
  menuName: string
  parentName?: string | null
  parentId: number
  orderNum: number
  path: string
  component?: string | null
  query?: string | null
  isFrame: '0' | '1' // 是否为外链 0-否 1-是
  isCache: '0' | '1' // 是否缓存 0-不缓存 1-缓存
  menuType: 'M' | 'C' | 'F' // 菜单类型 M-目录 C-菜单 F-按钮
  visible: '0' | '1' // 显示状态 0-显示 1-隐藏
  status: '0' | '1' // 菜单状态 0-正常 1-停用
  perms?: string // 权限标识
  icon?: string
  parameter?: string | null
  children?: BackendRoute[]
}

/**
 * 将后端路由数据转换为项目路由格式
 * @param backendRoute 后端路由数据
 * @returns 项目路由格式数据
 */
export function transformBackendRoute(backendRoute: BackendRoute): AppRoute.RowRoute {
  return {
    // 基础路由信息
    name: backendRoute.menuName,
    path: backendRoute.isFrame === '0' ? '/iframe' : backendRoute.path,
    componentPath: backendRoute.component,
    id: backendRoute.menuId,
    pid: backendRoute.parentId,

    // Meta 信息
    title: backendRoute.menuName,
    icon: backendRoute.icon,
    keepAlive: backendRoute.isCache === '0',
    hide: backendRoute.visible === '1', // visible为1表示隐藏
    order: backendRoute.orderNum,
    menuType: backendRoute.menuType === 'M' ? 'dir' : 'page',

    // 外链处理
    href: backendRoute.isFrame === '0' ? backendRoute.path : undefined,

    // 其他默认配置
    requiresAuth: true, // 默认需要认证
    roles: undefined, // 可根据 perms 字段进一步处理权限
    activeMenu: undefined,
    withoutTab: false, // 目录类型不显示在标签页
    pinTab: false,
  }
}

/**
 * 批量转换后端路由数据
 * @param backendRoutes 后端路由数据数组
 * @returns 项目路由格式数据数组
 */
export function transformBackendRoutes(backendRoutes: BackendRoute[]): AppRoute.RowRoute[] {
  return backendRoutes
    .filter(i => i.menuType !== 'F')
    .filter(i => i.status === '0')
    .map(transformBackendRoute)
}

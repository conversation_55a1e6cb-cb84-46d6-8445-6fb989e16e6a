<script setup lang="ts">
import { computed, ref } from 'vue'
import { NDatePicker, NRadioButton, NRadioGroup, useThemeVars } from 'naive-ui'

import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import type { ECOption } from '@/hooks/useEcharts'

const activeTab = ref('day')
const startTime = ref<number | null>(Date.now() - 24 * 60 * 60 * 1000)
const themeVars = useThemeVars()

const chartOptions = computed<ECOption>(() => {
  // 根据选中的标签生成不同的数据
  let times, peakDischarge, shoulderDischarge, flatDischarge, valleyDischarge, deepValleyDischarge
  let peakFee, shoulderFee, flatFee, valleyFee, deepValleyFee

  switch (activeTab.value) {
    case 'day':
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      peakDischarge = [15, 12, 18, 20, 16, 19, 17]
      shoulderDischarge = [12, 10, 15, 18, 14, 16, 15]
      flatDischarge = [8, 7, 10, 12, 9, 11, 10]
      valleyDischarge = [5, 4, 7, 8, 6, 8, 7]
      deepValleyDischarge = [3, 2, 4, 5, 3, 5, 4]
      peakFee = [4.5, 3.6, 5.4, 6.0, 4.8, 5.7, 5.1]
      shoulderFee = [3.6, 3.0, 4.5, 5.4, 4.2, 4.8, 4.5]
      flatFee = [2.4, 2.1, 3.0, 3.6, 2.7, 3.3, 3.0]
      valleyFee = [1.5, 1.2, 2.1, 2.4, 1.8, 2.4, 2.1]
      deepValleyFee = [0.9, 0.6, 1.2, 1.5, 0.9, 1.5, 1.2]
      break
    case 'month':
      times = ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
      peakDischarge = [450, 480, 420, 500, 470, 460, 450]
      shoulderDischarge = [360, 380, 340, 400, 370, 360, 350]
      flatDischarge = [240, 260, 220, 280, 250, 240, 230]
      valleyDischarge = [150, 160, 140, 180, 170, 160, 150]
      deepValleyDischarge = [90, 100, 80, 120, 110, 100, 90]
      peakFee = [135, 144, 126, 150, 141, 138, 135]
      shoulderFee = [108, 114, 102, 120, 111, 108, 105]
      flatFee = [72, 78, 66, 84, 75, 72, 69]
      valleyFee = [45, 48, 42, 54, 51, 48, 45]
      deepValleyFee = [27, 30, 24, 36, 33, 30, 27]
      break
    case 'year':
      times = ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
      peakDischarge = [13500, 12800, 14200, 13000, 13800, 13500, 13200]
      shoulderDischarge = [10800, 10200, 11400, 10400, 11000, 10800, 10600]
      flatDischarge = [7200, 6800, 7600, 7000, 7400, 7200, 7000]
      valleyDischarge = [4500, 4200, 4700, 4300, 4600, 4500, 4400]
      deepValleyDischarge = [2700, 2500, 2800, 2600, 2800, 2700, 2600]
      peakFee = [4050, 3840, 4260, 3900, 4140, 4050, 3960]
      shoulderFee = [3240, 3060, 3420, 3120, 3300, 3240, 3180]
      flatFee = [2160, 2040, 2280, 2100, 2220, 2160, 2100]
      valleyFee = [1350, 1260, 1410, 1290, 1380, 1350, 1320]
      deepValleyFee = [810, 750, 840, 780, 840, 810, 780]
      break
    default:
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      peakDischarge = [15, 12, 18, 20, 16, 19, 17]
      shoulderDischarge = [12, 10, 15, 18, 14, 16, 15]
      flatDischarge = [8, 7, 10, 12, 9, 11, 10]
      valleyDischarge = [5, 4, 7, 8, 6, 8, 7]
      deepValleyDischarge = [3, 2, 4, 5, 3, 5, 4]
      peakFee = [4.5, 3.6, 5.4, 6.0, 4.8, 5.7, 5.1]
      shoulderFee = [3.6, 3.0, 4.5, 5.4, 4.2, 4.8, 4.5]
      flatFee = [2.4, 2.1, 3.0, 3.6, 2.7, 3.3, 3.0]
      valleyFee = [1.5, 1.2, 2.1, 2.4, 1.8, 2.4, 2.1]
      deepValleyFee = [0.9, 0.6, 1.2, 1.5, 0.9, 1.5, 1.2]
  }

  // 构建系列数据
  const series: any[] = [
    // 堆叠柱状图 - 放电量
    {
      name: '尖放电量',
      type: 'bar',
      stack: 'discharge',
      barWidth: 15,
      label: {
        show: false,
      },
      data: peakDischarge,
    },
    {
      name: '峰放电量',
      type: 'bar',
      stack: 'discharge',
      label: {
        show: false,
      },
      data: shoulderDischarge,
    },
    {
      name: '平放电量',
      type: 'bar',
      stack: 'discharge',
      label: {
        show: false,
      },
      data: flatDischarge,
    },
    {
      name: '谷放电量',
      type: 'bar',
      stack: 'discharge',
      label: {
        show: false,
      },
      data: valleyDischarge,
    },
    {
      name: '深谷放电量',
      type: 'bar',
      stack: 'discharge',
      label: {
        show: false,
      },
      data: deepValleyDischarge,
    },
    // 折线图 - 电费
    {
      name: '尖电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: peakFee,
    },
    {
      name: '峰电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: shoulderFee,
    },
    {
      name: '平电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: flatFee,
    },
    {
      name: '谷电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: valleyFee,
    },
    {
      name: '深谷电费',
      type: 'line',
      yAxisIndex: 1,
      smooth: false,
      label: {
        show: false,
      },
      lineStyle: {
        width: 2,
      },
      data: deepValleyFee,
    },
  ]

  return {
    color: ['#2090ff', '#fbba4f', '#55cd92', '#4ebaf8', '#8ddbcf', '#2090ff', '#fbba4f', '#55cd92', '#4ebaf8', '#8ddbcf'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter(params: any) {
        let rValue = `${params[0].name}<br>`
        params.forEach((v: any) => {
          if (v.data !== 0 && v.data !== '-' && v.data !== undefined) {
            const unit = v.seriesName.includes('电费') ? '元' : 'kwh'
            rValue += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${v.color}"></span>${v.seriesName}: ${v.data}${unit}<br>`
          }
        })
        return rValue
      },
    },
    legend: [
      {
        icon: 'circle',
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 10,
        data: ['尖放电量', '峰放电量', '平放电量', '谷放电量', '深谷放电量'],
        right: '10%',
        top: '2%',
        textStyle: {
          fontSize: 12,
          color: themeVars.value.textColor1,
        },
      },
      {
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 10,
        data: ['尖电费', '峰电费', '平电费', '谷电费', '深谷电费'],
        right: '10%',
        top: '10%',
        textStyle: {
          fontSize: 12,
          color: themeVars.value.textColor1,
        },
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: times,
      axisLabel: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '单位: kwh',
        nameTextStyle: {
          color: themeVars.value.textColor1,
        },
        axisLabel: {
          color: themeVars.value.textColor1,
        },
        axisLine: {
          lineStyle: {
            color: themeVars.value.textColor1,
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: themeVars.value.borderColor,
          },
        },
      },
      {
        type: 'value',
        name: '单位: 元',
        nameTextStyle: {
          color: themeVars.value.textColor1,
        },
        axisLabel: {
          color: themeVars.value.textColor1,
        },
        axisLine: {
          lineStyle: {
            color: themeVars.value.textColor1,
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: '20%',
      containLabel: true,
    },
    series,
  }
})

useEcharts('dischargeChart', chartOptions)
</script>

<template>
  <BCard title="放电收入分析">
    <template #extend>
      <div class="flex items-center gap-2">
        <NDatePicker
          v-model:value="startTime"
          type="datetimerange"
          placeholder="开始时间"
          size="small"
        />
        <NRadioGroup v-model:value="activeTab" size="small" type="line">
          <NRadioButton value="day" label="日" />
          <NRadioButton value="month" label="月" />
          <NRadioButton value="year" label="年" />
        </NRadioGroup>
      </div>
    </template>
    <div ref="dischargeChart" class="w-full h-full" />
  </BCard>
</template>

<style scoped>
:deep(.n-card__content) {
  height: calc(100% - 50px);
}
</style>

<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import { changeRoleStatus, delRole, listRole } from '@/service/api/system/role'
import type { RoleQueryParams } from '@/service/api/system/role'
import { createTableColumns, searchColumns } from './config/columns'
import type { SearchFormData } from './config/columns'

// 导入子组件
import RoleModal from './components/RoleModal.vue'
import DataScopeModal from './components/DataScopeModal.vue'
import UserAssignModal from './components/UserAssignModal.vue'

// 弹窗引用
const modalRef = ref()
const dataScopeModalRef = ref()
const userAssignModalRef = ref()

// 创建搜索表单
const searchForm = createProSearchForm<SearchFormData>({
  defaultCollapsed: true,
  initialValues: {
    roleName: '',
    roleKey: '',
    status: undefined,
    createTime: undefined,
  },
})

// 使用useNDataTable
const {
  table: {
    tableProps,
  },
  search: {
    proSearchFormProps,
    submit,
    searchLoading,
  },
} = useNDataTable(getList, {
  form: searchForm,
})

// 批量删除相关
const checkedRowKeys = ref<number[]>([])

/** 查询角色列表 */
async function getList({ current, pageSize }: any, formData: SearchFormData) {
  try {
    const params: RoleQueryParams = {
      pageNum: current,
      pageSize,
      ...formData,
    }

    // 处理日期范围
    if (formData.createTime) {
      params.params = {}
      params.params.beginTime = formData.createTime[0]
      params.params.endTime = formData.createTime[1]
      delete (params as any).createTime
    }

    return listRole(params).then(res => ({
      total: res.total,
      list: res.rows,
    }))
  }
  catch (error) {
    console.error('获取角色列表失败:', error)
    return {
      total: 0,
      list: [],
    }
  }
}

/** 删除角色 */
async function deleteRole(roleId: number | number[]) {
  const isBatch = Array.isArray(roleId)

  window.$dialog.warning({
    title: '确认删除',
    content: isBatch
      ? `是否确认删除选中的 ${roleId.length} 个角色？`
      : '是否确认删除该角色？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await delRole(roleId.toString())
        window.$message.success(isBatch ? '批量删除成功' : '删除成功')
        // 清空选中项
        if (isBatch) {
          checkedRowKeys.value = []
        }
        submit()
      }
      catch (error) {
        console.error('删除角色失败:', error)
        window.$message.error(isBatch ? '批量删除失败' : '删除失败')
      }
    },
  })
}

/** 处理状态切换 */
async function handleUpdateStatus(roleId: number, status: '0' | '1') {
  try {
    await changeRoleStatus({ roleId, status })
    window.$message.success(status === '0' ? '角色已启用' : '角色已禁用')
    submit()
  }
  catch (error) {
    console.error('更新角色状态失败:', error)
    window.$message.error('状态更新失败')
  }
}

// 创建表格列配置
const columns = createTableColumns({
  onEdit: row => modalRef.value.openModal('edit', row),
  onDelete: deleteRole,
  onStatusChange: handleUpdateStatus,
  onDataScope: row => dataScopeModalRef.value.openModal(row),
  onUserAssign: row => userAssignModalRef.value.openModal(row),
})
</script>

<template>
  <div>
    <pro-search-form
      v-bind="proSearchFormProps"
      :form="searchForm"
      :columns="searchColumns"
    />

    <!-- 数据表格 -->
    <pro-data-table
      v-bind="tableProps"
      v-model:checked-row-keys="checkedRowKeys"
      :columns="columns"
      row-key="roleId"
      :loading="searchLoading"
    >
      <template #title>
        <n-button type="primary" @click="modalRef.openModal('add')">
          <template #icon>
            <icon-park-outline-plus />
          </template>
          新增角色
        </n-button>
      </template>
      <template #toolbar>
        <n-flex>
          <n-button
            type="error"
            :disabled="checkedRowKeys.length === 0"
            @click="deleteRole(checkedRowKeys)"
          >
            <template #icon>
              <icon-park-outline-delete />
            </template>
            删除
          </n-button>
        </n-flex>
      </template>
    </pro-data-table>

    <RoleModal ref="modalRef" modal-name="角色" @success="submit" />
    <DataScopeModal ref="dataScopeModalRef" @success="submit" />
    <UserAssignModal ref="userAssignModalRef" @success="submit" />
  </div>
</template>

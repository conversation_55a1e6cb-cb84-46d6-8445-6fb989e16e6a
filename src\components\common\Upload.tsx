import type { FunctionalComponent } from 'vue'
import type { UploadProps } from 'naive-ui'
import { NImage, NUpload } from 'naive-ui'
import { request } from '@/service/http'
import { local } from '@/utils'

export const Upload: FunctionalComponent<UploadProps> = (props, ctx) => {
  return (
    <NUpload
      action={`${request.options.baseURL}/common/upload`}
      headers={{
        Authorization: `Bearer ${local.get('accessToken')}`,
      }}
      name="file"
      {...props}
    >
      {{ ...ctx.slots }}
    </NUpload>
  )
}

export const ImageUpload: FunctionalComponent<
  UploadProps & { value: string },
  { 'update:value': (value: string) => void }
> = (props, ctx) => {
  const onFinish: UploadProps['onFinish'] = ({ file, event }) => {
    const request = event?.target as XMLHttpRequest | undefined

    if (
      request
      && request.response
      && request.response.url
    ) {
      ctx.emit('update:value', request.response.url)
    }
    return file
  }

  if (props.value) {
    return h(NImage, { src: request.options.baseURL + props.value, style: { maxWidth: '200px' } })
  }

  return h(Upload, {
    ...props,
    'response-type': 'json',
    onFinish,
    'showFileList': false,
  }, ctx.slots)
}

<script setup lang="ts">
import useDragAndDrop from '@/views/setting/site/editor/hooks/useDnD'
import { useBasicNodes } from '@/views/setting/site/editor/hooks/useBasicNodes'

const { onDragStart } = useDragAndDrop()

const { nodes } = useBasicNodes()
</script>

<template>
  <div>
    <main class="flex gap-5 flex-wrap">
      <div
        v-for="node in nodes"
        :key="node.name"
        class="border border-solid rounded border-[#96ccff] w-[80px] h-[80px] flex flex-col items-center justify-center gap-1"
        :draggable="true"
        @dragstart="onDragStart($event, node)"
      >
        <img class="h-[45px]" height="51" :src="node.image" alt="icon">
        <NText class="whitespace-nowrap">
          {{ node.name }}
        </NText>
      </div>
    </main>
  </div>
</template>

<style scoped>

</style>

<script setup lang="ts">
import BHeader from '@/components/big-screen/BHeader.vue'
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
import QueryForm from './components/QueryForm.vue'
import GridElectricityChart from './components/GridElectricityChart.vue'
import LoadElectricityChart from './components/LoadElectricityChart.vue'
import YearOnYearChart from './components/YearOnYearChart.vue'
import ElectricityAnalysisChart from './components/ElectricityAnalysisChart.vue'
</script>

<template>
  <EnergyStorageBackground class="h-full relative">
    <BHeader title="分布式储能运营云平台" />
    <main class="consumption-dashboard h-[calc(100%-80px)] relative z-1 p-3 pt-0">
      <!-- 查询表单 -->
      <QueryForm class="mb-3" />

      <!-- 图表区域 -->
      <div class="charts-grid">
        <GridElectricityChart />
        <LoadElectricityChart />
        <YearOnYearChart />
        <ElectricityAnalysisChart />
      </div>
    </main>
  </EnergyStorageBackground>
</template>

<style lang="scss" scoped>
.consumption-dashboard {
  .charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 353px 377px;
    gap: 12px;
    height: calc(100% - 120px);
  }
}
</style>

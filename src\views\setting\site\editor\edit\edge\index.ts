import type { Raw } from 'vue'
import WireEditor from '@/views/setting/site/editor/edit/edge/WireEditor.vue'
import NetworkEditor from '@/views/setting/site/editor/edit/edge/NetworkEditor.vue'
import { CustomEdgeType } from '@/views/setting/site/editor/edges'

export const EdgeEditors: Record<CustomEdgeType, Raw<Component>> = {
  [CustomEdgeType.Wire]: markRaw(WireEditor),
  [CustomEdgeType.Network]: markRaw(NetworkEditor),
}

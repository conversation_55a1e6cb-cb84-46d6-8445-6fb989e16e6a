<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { delData, listData, updateData } from '@/service/api/system/dict-data'
import { useDictStore } from '@/store'
import type { DictDataSearchFormData } from '../columns'
import {
  createDataColumns,
  createDataSearchColumns,
} from '../columns'

// 导入子组件
import DataForm from './DataForm.vue'

const props = defineProps<{
  dictType: Entity.DictType | null
}>()

// 使用 defineModel 进行双向绑定
const visible = defineModel<boolean>('visible', { default: false })

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)
const checkedDataIds = ref<number[]>([])
// 弹窗引用
const dataFormRef = ref()

// 创建字典数据搜索表单
const dataSearchForm = createProSearchForm<DictDataSearchFormData>({
  initialValues: {
    dictType: undefined,
    dictValue: '',
    status: undefined,
  },
})

// 使用useNDataTable
const {
  table: {
    tableProps,
  },
  search: {
    proSearchFormProps,
    submit,
    reset,
  },
} = useNDataTable(getDataList, {
  form: dataSearchForm,
})

// 动态搜索列配置
const dataSearchColumns = ref<any[]>([])

// 在组件挂载时初始化
onMounted(async () => {
  dataSearchColumns.value = await createDataSearchColumns()
})

/** 查询字典数据列表 */
async function getDataList({ current, pageSize }: any, formData: DictDataSearchFormData) {
  startLoading()
  try {
    const params = {
      pageNum: current,
      pageSize,
      dictType: formData.dictType || props.dictType!.dictType, // 优先使用搜索表单中的字典类型
      dictValue: formData.dictValue,
      status: formData.status,
    }
    return listData(params).then(res => ({
      total: res.total,
      list: res.rows,
    }))
  }
  catch (error) {
    console.error('获取字典数据列表失败:', error)
    return {
      total: 0,
      list: [],
    }
  }
  finally {
    endLoading()
  }
}

// 使用提取的字典数据表格列配置
const dataColumns = createDataColumns({
  onEdit: row => dataFormRef.value?.openModal('edit', row),
  onDelete: dictCode => handleDataDelete(dictCode),
  onStatusChange: async (row, status) => {
    try {
      await updateData({
        ...row,
        status,
      })
      window.$message.success('状态更新成功')
      submit()
    }
    catch (error) {
      console.error('状态更新失败:', error)
      window.$message.error('状态更新失败')
    }
  },
})

/** 新增按钮操作 */
function handleDataAdd() {
  dataFormRef.value?.openModal('add', { dictType: props.dictType?.dictType })
}

/** 删除按钮操作 */
async function handleDataDelete(dictCodeIds: number | number[]) {
  const isBatch = Array.isArray(dictCodeIds)

  window.$dialog.warning({
    title: '确认删除',
    content: isBatch
      ? `是否确认删除选中的 ${dictCodeIds.length} 个字典数据？`
      : '是否确认删除该字典数据？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await delData(dictCodeIds.toString())
        // 清空选中项
        if (isBatch) {
          checkedDataIds.value = []
        }
        window.$message.success('删除成功')
        useDictStore().removeDict(props.dictType?.dictType || '')
        submit()
      }
      catch (error: any) {
        console.error('删除失败:', error)
      }
    },
  })
}

// 监听抽屉打开，重置搜索表单并刷新数据
watch(() => visible.value, (newVisible) => {
  if (newVisible && props.dictType) {
    // 重置字典数据搜索表单
    checkedDataIds.value = []
    reset()
  }
})
</script>

<template>
  <n-drawer
    v-model:show="visible"
    :width="1200"
    placement="right"
    :trap-focus="false"
    :block-scroll="false"
  >
    <n-drawer-content
      :title="`字典数据 - ${dictType?.dictName || ''}`"
      closable
    >
      <!-- 字典数据ProSearchForm -->
      <pro-search-form
        v-bind="proSearchFormProps"
        :form="dataSearchForm"
        :columns="dataSearchColumns"
        :collapse-button-props="false"
      />

      <!-- 字典数据表格 -->
      <pro-data-table
        v-bind="tableProps"
        v-model:checked-row-keys="checkedDataIds"
        :loading="loading"
        :columns="dataColumns"
        row-key="dictCode"
      >
        <template #title>
          <n-button type="primary" @click="handleDataAdd">
            <template #icon>
              <icon-park-outline-plus />
            </template>
            新增字典数据
          </n-button>
        </template>
        <template #toolbar>
          <n-space>
            <n-button
              type="error"
              :disabled="checkedDataIds.length === 0"
              @click="handleDataDelete(checkedDataIds)"
            >
              <template #icon>
                <icon-park-outline-delete />
              </template>
              删除
            </n-button>
          </n-space>
        </template>
      </pro-data-table>

      <!-- 添加或修改字典数据对话框 -->
      <DataForm
        ref="dataFormRef"
        modal-name="字典数据"
        @success="submit"
      />
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
import Analysis from './components/Analysis.vue'
import Distribution from './components/Distribution.vue'

const analysisType = ref(0)
</script>

<template>
  <EnergyStorageBackground class="h-full">
    <BHeader title="分布式储能运营云平台" />
    <main class=" h-[calc(100%-80px)] grid grid-rows-[auto_1fr_1fr] gap-3 p-3">
      <div class="flex gap-3">
        <n-select class="w-300px" />
        <n-select class="w-300px" />
        <n-radio-group v-modal:value="analysisType" class="ml-auto">
          <n-radio-button :value="0">
            温度分析
          </n-radio-button>
          <n-radio-button :value="1">
            电压分析
          </n-radio-button>
        </n-radio-group>
      </div>

      <Analysis />
      <Distribution />
    </main>
  </EnergyStorageBackground>
</template>

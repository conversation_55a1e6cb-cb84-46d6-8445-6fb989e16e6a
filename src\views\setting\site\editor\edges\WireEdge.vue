<script lang="ts" setup>
import type { EdgeProps } from '@vue-flow/core'
import { BaseEdge, getBezierPath } from '@vue-flow/core'
import { computed } from 'vue'

defineOptions({ inheritAttrs: false })

const props = defineProps<EdgeProps>()

const path = computed(() => getBezierPath(props))
</script>

<template>
  <BaseEdge
    :id="id"
    :style="{
      ...style,
      stroke: '#FFD700', // 高压电线常用的金色
      strokeWidth: 4, // 更粗的线宽模拟高压电线
      strokeDasharray: 'none', // 实线
      filter: 'drop-shadow(0 0 2px rgba(255, 215, 0, 0.7))', // 高压电发光效果
    }"
    :path="path[0]"
    :marker-end="markerEnd"
    :label="data.text"
    :label-x="path[1]"
    :label-y="path[2]"
    :label-style="{
      fill: 'black',
      fontWeight: 'bold',
      fontSize: '11px',
      textShadow: '0 0 2px white',
    }"
    :label-show-bg="true"
    :label-bg-style="{
      fill: 'rgba(255, 255, 255, 0.8)',
      stroke: '#FFD700',
      strokeWidth: 1,
    }"
    :label-bg-padding="[4, 8]"
    :label-bg-border-radius="4"
  />
</template>

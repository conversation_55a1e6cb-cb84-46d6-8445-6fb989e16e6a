<script setup lang="ts">
import type { ECOption } from '@/hooks'
import { useEcharts } from '@/hooks'

const xLabel = ['2025-08-01', '2025-08-02', '2025-08-03', '2025-08-04', '2025-08-05', '2025-08-06', '2025-08-07']
const data = [
  [120, 132, 601, 134, 90, 230, 210],
  [220, 182, 191, 234, 290, 330, 310],
  [150, 232, 201, 154, 190, 330, 410],
]

const chatOptions = computed<ECOption>(() => {
  return {
    // 定义全局颜色，确保线条和图例颜色一致
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      left: '1%',
      right: '3%',
      bottom: '0%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xLabel,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },

    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#CFE6FC',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
      },
    },
    series: [
      {
        color: '#F7C065',
        name: '单体温度平均值',
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0.25,
                color: 'rgba(247, 192, 101, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(247, 192, 101, 0)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
        data: data[0],
      },
      {
        color: '#37a2da',
        name: '单体温度最大值',
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(55, 162, 218, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(55, 162, 218, 0)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
        data: data[1],
      },
      {
        color: '#61C26A',
        name: '单体温度最小值',
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0.25,
                color: 'rgba(159, 230, 184, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(159, 230, 184, 0)',
              },
            ],
          },
        },

        emphasis: {
          focus: 'series',
        },
        data: data[2],
      },

    ],
    legend: {},
  }
})

useEcharts('echarts', chatOptions)
</script>

<template>
  <BCard title="单体温度/电压分析">
    <template #extend>
      <n-date-picker type="daterange" size="small" />
      <n-button type="primary" size="small">
        查询
      </n-button>
    </template>

    <div ref="echarts" class="h-full" />
  </BCard>
</template>

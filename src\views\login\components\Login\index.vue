<script setup lang="ts">
import type { FormInst } from 'naive-ui'
import { useAuthStore } from '@/store'
import { local } from '@/utils'
import type { LoginParams } from '@/service'
import { getCodeImg } from '@/service'
// const emit = defineEmits(['update:modelValue'])

const authStore = useAuthStore()

// function toOtherForm(type: any) {
//   emit('update:modelValue', type)
// }

const { t } = useI18n()
const rules = computed(() => {
  return {
    username: {
      required: true,
      trigger: 'blur',
      message: t('login.accountRuleTip'),
    },
    password: {
      required: true,
      trigger: 'blur',
      message: t('login.passwordRuleTip'),
    },
    code: {
      required: true,
      trigger: 'blur',
      message: '请输入验证码',
    },
  }
})
const formValue = ref<LoginParams>({
  username: 'admin',
  password: 'psh!@#123',
  code: '',
  uuid: '',
})

const codeImg = ref<string>()
const enableCode = ref(false)
const isRemember = ref(false)
const isLoading = ref(false)

const formRef = ref<FormInst | null>(null)

function handleLogin() {
  formRef.value?.validate(async (errors) => {
    if (errors)
      return

    isLoading.value = true

    const { username, password, code, uuid } = formValue.value
    const loginSuccess = await authStore.login(username, password, code, uuid)

    if (!loginSuccess) {
      refreshCode()
    }

    if (isRemember.value)
      local.set('loginAccount', formValue.value)
    else
      local.remove('loginAccount')

    isLoading.value = false
  })
}

// 刷新验证码
function refreshCode() {
  getCodeImg().then((res) => {
    if (res.captchaEnabled === false) {
      enableCode.value = false
      return
    }
    codeImg.value = res.img
    formValue.value.uuid = res.uuid
    formValue.value.code = '' // 清空验证码输入
  }).catch(() => {
    window.$message?.error('获取验证码异常，请重试')
  })
}

onMounted(() => {
  checkUserAccount()
  refreshCode()
})

function checkUserAccount() {
  const loginAccount = local.get('loginAccount')
  if (!loginAccount)
    return

  formValue.value = loginAccount
  isRemember.value = true
}
</script>

<template>
  <div>
    <n-h2 depth="3" class="flex flex-col items-center mb-60px">
      <p>
        {{ $t('login.signInTitle') }}
      </p>
      <p class="w-57px h-7px rounded-lg" style="background-color: var(--primary-color)" />
    </n-h2>
    <n-form ref="formRef" :rules="rules" :model="formValue" :show-label="false" size="large">
      <n-form-item path="username">
        <n-input v-model:value.trim="formValue.username" clearable :placeholder="$t('login.accountPlaceholder')">
          <template #prefix>
            <nova-icon icon="ant-design:user-outlined" />
          </template>
        </n-input>
      </n-form-item>
      <n-form-item path="password">
        <n-input
          v-model:value.trim="formValue.password" type="password"
          :placeholder="$t('login.passwordPlaceholder')" clearable
          show-password-on="click"
        >
          <template #prefix>
            <nova-icon icon="ant-design:lock-outlined" />
          </template>
          <template #password-invisible-icon>
            <icon-park-outline-preview-close-one />
          </template>
          <template #password-visible-icon>
            <icon-park-outline-preview-open />
          </template>
        </n-input>
      </n-form-item>
      <n-form-item v-if="enableCode" path="code">
        <n-input v-model:value.trim="formValue.code" placeholder="请输入验证码" clearable>
          <template #prefix>
            <nova-icon icon="ant-design:safety-certificate-outlined" />
          </template>
          <template #suffix>
            <img
              v-if="codeImg"
              :src="`data:image/png;base64,${codeImg}`"
              alt="验证码"
              class="cursor-pointer w-23 ml-2 object-contain"
              @click="refreshCode"
            >
          </template>
        </n-input>
      </n-form-item>
      <n-space vertical :size="20">
        <div class="flex-y-center">
          <n-checkbox v-model:checked="isRemember">
            {{ $t('login.rememberMe') }}
          </n-checkbox>
          <!-- <n-button type="primary" text @click="toOtherForm('resetPwd')">
            {{ $t('login.forgotPassword') }}
          </n-button> -->
        </div>
        <n-button block type="primary" size="large" :loading="isLoading" :disabled="isLoading" @click="handleLogin">
          {{ $t('login.signIn') }}
        </n-button>
        <!-- <n-flex>
          <n-text>{{ $t('login.noAccountText') }}</n-text>
          <n-button type="primary" text @click="toOtherForm('register')">
            {{ $t('login.signUp') }}
          </n-button>
        </n-flex> -->
      </n-space>
    </n-form>
  </div>
</template>

<style scoped></style>

<script setup lang="ts">
import { naiveI18nOptions } from '@/utils'
import { darkTheme } from 'naive-ui'
import { useAppStore } from './store'

const appStore = useAppStore()

const naiveLocale = computed(() => {
  return naiveI18nOptions[appStore.lang] ? naiveI18nOptions[appStore.lang] : naiveI18nOptions.enUS
},
)

const propOverrides = {
  ProSearchForm: {
    cols: 4,
  },
  ProDataTable: {
    size: 'small',
    bordered: false,
    singleLine: false,
    striped: true,
    tableCardProps: {
      size: 'small',
      bordered: false,
    },
  },
  ProForm: {
    labelPlacement: 'left',
  },
  ProModalForm: {
    labelPlacement: 'left',
    preset: 'card',
    labelWidth: 90,
    segmented: {
      content: true,
      footer: true,
    },
  },
}
</script>

<template>
  <ProConfigProvider
    abstract inline-theme-disabled
    :prop-overrides="propOverrides"
    :theme="appStore.colorMode === 'dark' ? darkTheme : null"
    :locale="naiveLocale.locale"
    :date-locale="naiveLocale.dateLocale"
    :theme-overrides="appStore.theme"
  >
    <naive-provider>
      <router-view />
      <Watermark :show-watermark="appStore.showWatermark" />
    </naive-provider>
  </ProConfigProvider>
</template>

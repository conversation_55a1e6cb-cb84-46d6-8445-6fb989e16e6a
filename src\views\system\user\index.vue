<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import { changeUserStatus, delUser, delUsers, deptTreeSelect, exportUser, listUser } from '@/service/api/system/user'
import type { TreeOption } from 'naive-ui'
import type { UserQueryParams } from '@/service/api/system/user'
import { createTableColumns, searchColumns } from './columns'
import type { SearchFormData } from './columns'
import { saveAs } from 'file-saver'
// 导入子组件
import UserModal from './components/UserModal.vue'
import ImportModal from './components/ImportModal.vue'
import ResetPasswordModal from './components/ResetPasswordModal.vue'

// 弹窗引用
const modalRef = ref()
const resetPasswordModalRef = ref()
const importModalRef = ref()

// 创建搜索表单
const searchForm = createProSearchForm<Partial<SearchFormData>>({
  defaultCollapsed: true,
  initialValues: {
    userName: '',
    phonenumber: '',
    status: undefined,
    deptId: undefined,
    createTime: undefined,
  },
})

// 使用useNDataTable
const {
  table: {
    tableProps,
  },
  search: {
    proSearchFormProps,
    searchLoading,
    submit,
  },
} = useNDataTable(getList, {
  form: searchForm,
})

// 批量删除相关
const checkedRowKeys = ref<(string | number)[]>([])

// 部门树数据
const treeData = ref<Api.TreeNode[]>([])

/** 查询用户列表 */
interface Result {
  total: number
  list: Entity.UserInfo[]
}
async function getList({ current, pageSize }: any, formData: Partial<SearchFormData>): Promise<Result> {
  try {
    const params: UserQueryParams = {
      ...formData,
      pageNum: current,
      pageSize,
    }

    // 处理日期范围
    if (formData.createTime) {
      params.params = {}
      params.params.beginTime = formData.createTime[0]
      params.params.endTime = formData.createTime[1]
      delete (params as any).createTime
    }

    return listUser(params).then(res => ({
      total: res.total,
      list: res.rows,
    }))
  }
  catch (error) {
    console.error('获取用户列表失败:', error)
    return {
      total: 0,
      list: [],
    }
  }
}

/** 删除用户 */
async function deleteUser(userId: number | number[] | (string | number)[]) {
  const isBatch = Array.isArray(userId)

  window.$dialog.warning({
    title: '确认删除',
    content: isBatch
      ? `是否确认删除选中的 ${userId.length} 个用户？`
      : '是否确认删除该用户？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        if (isBatch) {
          await delUsers(userId.toString())
          window.$message.success('批量删除成功')
          // 清空选中项
          if (isBatch) {
            checkedRowKeys.value = []
          }
        }
        else {
          await delUser(Number(userId))
          window.$message.success('删除成功')
        }
        // 刷新表格
        submit()
      }
      catch (error) {
        console.error('删除用户失败:', error)
        window.$message.error(isBatch ? '批量删除失败' : '删除失败')
      }
    },
  })
}

// 创建表格列配置
const columns = createTableColumns({
  onEdit: row => modalRef.value.openModal('edit', row),
  onDelete: deleteUser,
  onStatusChange: handleUpdateStatus,
  onResetPassword: handleResetPassword,
})

/** 处理状态切换 */
async function handleUpdateStatus(userId: number, status: '0' | '1') {
  try {
    await changeUserStatus({ userId, status })
    window.$message.success(status === '0' ? '用户已启用' : '用户已禁用')
    submit()
  }
  catch (error) {
    console.error('更新用户状态失败:', error)
  }
}

// 获取部门树数据
async function getDeptTree() {
  try {
    const { data } = await deptTreeSelect()
    treeData.value = data
  }
  catch (error) {
    console.error('获取部门树失败:', error)
    treeData.value = []
  }
}

// 重置密码
function handleResetPassword(user: Entity.UserInfo) {
  resetPasswordModalRef.value?.openModal(user)
}

/** 导出用户数据 */
async function handleExport() {
  try {
    const formValues = searchForm.fieldsValue.value
    const params: UserQueryParams = {
      ...formValues,
    }

    // 处理日期范围
    if (formValues.createTime && Array.isArray(formValues.createTime)) {
      params.params = {}
      params.params.beginTime = formValues.createTime[0]
      params.params.endTime = formValues.createTime[1]
      delete (params as any).createTime
    }

    const blob = await exportUser(params)
    saveAs(blob, `用户数据_${new Date().getTime()}.xlsx`)
    window.$message.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    window.$message.error('导出失败')
  }
}

/** 显示导入弹窗 */
function showImportModal() {
  importModalRef.value?.show()
}

// 处理部门树节点选择
function handleDeptSelect(deptId: number) {
  // 更新搜索表单中的部门ID
  searchForm.values.value.deptId = deptId
  // 触发搜索
  submit()
}

onMounted(() => {
  getDeptTree()
})
</script>

<template>
  <div>
    <pro-search-form
      v-bind="proSearchFormProps"
      :form="searchForm"
      :columns="searchColumns"
    />

    <n-flex>
      <n-card class="w-70" size="small" title="部门" :bordered="false">
        <template #header-extra>
          <n-button text size="small" @click="getDeptTree">
            <template #icon>
              <icon-park-outline-refresh />
            </template>
          </n-button>
        </template>

        <n-tree
          block-line :data="treeData" key-field="id" :node-props="({ option }: { option: TreeOption }) => {
            return {
              onClick: () => handleDeptSelect(option.id as number),
            }
          }"
        />
      </n-card>

      <!-- 数据表格 -->
      <pro-data-table
        v-bind="tableProps"
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        row-key="userId"
        :loading="searchLoading"
        class="flex-1"
      >
        <template #title>
          <n-button type="primary" @click="modalRef.openModal('add')">
            <template #icon>
              <icon-park-outline-plus />
            </template>
            新增
          </n-button>
        </template>
        <template #toolbar>
          <n-flex>
            <n-button type="error" :disabled="checkedRowKeys.length === 0" @click="deleteUser(checkedRowKeys)">
              <template #icon>
                <icon-park-outline-delete />
              </template>
              删除
            </n-button>
            <n-button @click="showImportModal">
              <template #icon>
                <icon-park-outline-download />
              </template>
              导入
            </n-button>
            <n-button @click="handleExport">
              <template #icon>
                <icon-park-outline-upload />
              </template>
              导出
            </n-button>
          </n-flex>
        </template>
      </pro-data-table>
    </n-flex>

    <UserModal ref="modalRef" modal-name="用户" @success="submit" />
    <ImportModal ref="importModalRef" @success="submit" />
    <ResetPasswordModal ref="resetPasswordModalRef" @success="submit" />
  </div>
</template>

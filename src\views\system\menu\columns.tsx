import type { DataTableColumns } from 'naive-ui'
import type { ProSearchFormColumns } from 'pro-naive-ui'
import { renderProCopyableText } from 'pro-naive-ui'
import {
  NButton,
  NIcon,
  NSpace,
  NSwitch,
  NTag,
  NTooltip,
} from 'naive-ui'
import { createIcon } from '@/utils'
import PreviewClose from '~icons/icon-park-outline/preview-close-one'
import Folder from '~icons/icon-park-outline/folder-withdrawal'
import List from '~icons/icon-park-outline/mindmap-list'
import Permissions from '~icons/icon-park-outline/permissions'

// 搜索表单接口
export interface SearchFormData {
  menuName?: string
  status?: '0' | '1'
}

// 搜索表单配置
export const searchColumns: ProSearchFormColumns<SearchFormData> = [
  {
    title: '菜单名称',
    path: 'menuName',
    field: 'input',
    fieldProps: {
      clearable: true,
    },
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      clearable: true,
      options: [
        { label: '启用', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
  },
]

// 表格列配置
export function createTableColumns(options: {
  onEdit: (row: Entity.Menu) => void
  onDelete: (menuId: number) => void
  onStatusChange: (row: Entity.Menu, value: '0' | '1') => void
  onAdd: (row: Entity.Menu) => void
}): DataTableColumns<Entity.Menu> {
  const { onEdit, onDelete, onStatusChange, onAdd } = options

  return [
    {
      title: '菜单名称',
      key: 'menuName',
      width: 260,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        return (
          <div class="flex items-center gap-2">
            <span>{row.menuName}</span>
            {row.visible === '1' && (
              <NTooltip trigger="hover">
                {{
                  trigger: () => <PreviewClose />,
                  default: () => '此菜单已隐藏',
                }}
              </NTooltip>
            )}
          </div>
        )
      },
    },
    {
      title: '图标',
      align: 'center',
      key: 'icon',
      width: '6em',
      render: row => createIcon(row.icon, { size: 20 }),
    },
    {
      title: '路径',
      key: 'path',
      width: '500px',
      render: row => renderProCopyableText(row.path),
    },
    {
      title: '排序',
      key: 'orderNum',
      align: 'center',
      width: '6em',
    },
    {
      title: '菜单类型',
      align: 'center',
      key: 'menuType',
      width: '9em',
      render: (row) => {
        const typeMap = {
          M: { text: '目录', type: 'primary' as const, icon: Folder },
          C: { text: '菜单', type: 'warning' as const, icon: List },
          F: { text: '权限', type: 'info' as const, icon: Permissions },
        }
        const config = typeMap[row.menuType]
        return (
          <NTag type={config.type} bordered={false} class="text-md">
            {{
              default: () => config.text,
              icon: () => <NIcon size={18}>{h(config.icon)}</NIcon>,
            }}
          </NTag>
        )
      },
    },
    {
      title: '权限标识',
      key: 'perms',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
      render: row => renderProCopyableText(row.perms),
    },
    {
      title: '状态',
      align: 'center',
      key: 'status',
      width: '7em',
      render: (row) => {
        return (

          <NSwitch
            value={row.status}
            checkedValue="0"
            uncheckedValue="1"
            onUpdateValue={(value: '0' | '1') => onStatusChange(row, value)}
          >
            {{
              checked: () => '启用',
              unchecked: () => '停用',
            }}
          </NSwitch>
        )
      },
    },
    {
      title: '操作',
      align: 'center',
      key: 'actions',
      width: '14em',
      fixed: 'right',
      render: (row) => {
        return (
          <NSpace justify="center">
            {/* 新建按钮 - 权限数据不可以新建 */}
            {row.menuType !== 'F' && (
              <NButton
                v-hasPermi="system:menu:add"
                text
                type="primary"
                onClick={() => onAdd(row)}
              >
                新建
              </NButton>
            )}
            <NButton
              v-hasPermi="system:menu:edit"
              text
              onClick={() => onEdit(row)}
            >
              编辑
            </NButton>
            <NButton
              v-hasPermi="system:menu:remove"
              text
              type="error"
              onClick={() => onDelete(row.menuId)}
            >
              删除
            </NButton>
          </NSpace>
        )
      },
    },
  ]
}

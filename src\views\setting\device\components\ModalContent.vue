<script setup lang="ts">
import { useDict } from '@/hooks'

// 获取设备类型字典数据
const { options: deviceTypeOptions } = useDict('device_type')
</script>

<template>
  <div class="grid grid-cols-2">
    <ProSelect
      required
      title="设备类型"
      path="deviceType"
      :field-props="{ options: deviceTypeOptions }"
    />
    <ProInput
      title="设备编号"
      path="deviceId"
      required
    />
    <ProInput title="设备名称" path="deviceName" required />
    <ProInput title="设备型号" path="deviceModel" required />
    <ProInput title="设备单位" path="deviceUnit" required />
    <ProDigit title="设备数量" path="deviceNum" required />
    <ProDigit title="设备价格" path="price" required />
    <ProInput title="生产厂家" path="producer" required />
    <ProInput title="规格" path="specification" required />
    <ProInput title="维护周期" path="maintenanceCycle" required />
    <ProInput title="维护信息" path="maintenanceInfo" required />
    <ProTextarea :rows="3" title="备注" path="remark" class="col-span-2" />
  </div>
</template>

<style lang="scss" scoped>
.modal-content {
  display: grid;
  grid-template-rows: 1fr 1fr;
}
</style>

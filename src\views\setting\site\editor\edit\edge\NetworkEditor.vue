<script lang="ts" setup>
import type { FormRules } from 'naive-ui'
import type { EdgeProps } from '@vue-flow/core'

defineProps<EdgeProps>()

const rules: FormRules = {
  attr1: {
    required: true,
    message: '请输入属性1',
  },
}
</script>

<template>
  <NForm :rules="rules">
    <NFormItem path="attr1" label="网线属性1">
      <NInput
        v-model:value="data.payload.attr1"
      />
    </NFormItem>
  </NForm>
</template>

import type { App } from 'vue'
import { useAuthStore } from '@/store'

export function install(app: App) {
  function updatePermission(el: HTMLElement, permissions: string | string[]) {
    if (!permissions) {
      throw new Error('v-hasPermi 指令需要设置权限标识')
    }

    const authStore = useAuthStore()
    const userPermissions = authStore.permissions
    const allPermission = '*:*:*'

    // 确保 permissions 是数组
    const permissionArray = Array.isArray(permissions) ? permissions : [permissions]

    if (permissionArray.length === 0) {
      throw new Error('请设置操作权限标签值')
    }

    // 检查是否有权限
    const hasPermissions = userPermissions.some((permission) => {
      return allPermission === permission || permissionArray.includes(permission)
    })

    if (!hasPermissions) {
      el.parentNode?.removeChild(el)
    }
  }

  app.directive('hasPermi', {
    mounted(el, binding) {
      updatePermission(el, binding.value)
    },
  })
}

export default {
  install,
}

import WireEdge from '@/views/setting/site/editor/edges/WireEdge.vue'
import NetworkEdge from '@/views/setting/site/editor/edges/NetworkEdge.vue'
import type { DefaultEdgeOptions } from '@vue-flow/core'
import type { Raw } from 'vue'

export const DefaultEdgeType: DefaultEdgeOptions['type'] = 'smoothstep'

export enum CustomEdgeType {
  Wire = 'wire',
  Network = 'network',
}

export const edgeTypes: Record<CustomEdgeType, Raw<Component>> = {
  [CustomEdgeType.Wire]: markRaw(WireEdge),
  [CustomEdgeType.Network]: markRaw(NetworkEdge),
}

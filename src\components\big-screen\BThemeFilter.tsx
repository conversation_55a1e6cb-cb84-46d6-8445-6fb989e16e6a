import type { FunctionalComponent } from 'vue'
import { defineComponent } from 'vue'

import EnergyStorageBG from '@/assets/imgs/common/bscreen-bg.png'
import SettingBG from '@/assets/imgs/setting/bg.png'

export interface Props {
  tag?: keyof HTMLElementTagNameMap
  image: string
}

const BThemeFilter = defineComponent<Props>({
  props: {
    image: {
      type: String,
      required: true,
    },
    tag: {
      type: String,
    },
  },
  setup(props, { slots }) {
    return () => h(props.tag ?? 'div', { class: 'relative wh-full' }, [
      <img
        class="absolute wh-full object-fill dark:(filter-invert-100 filter-hue-rotate-180)"
        src={props.image}
        alt="background"
      />,
      slots.default?.(),
    ])
  },
})

export default BThemeFilter

export function backgroundFactory(props: Omit<Props, 'tag'>): FunctionalComponent<Omit<Props, 'image'>> {
  return (_props, ctx) => (
    <BThemeFilter
      image={props.image}
    >
      {ctx.slots?.default?.()}
    </BThemeFilter>
  )
}

export const EnergyStorageBackground: FunctionalComponent = backgroundFactory({ image: EnergyStorageBG })

export const SettingBackground: FunctionalComponent = backgroundFactory({ image: SettingBG })

<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { addType, getType, updateType } from '@/service/api/system/dict-type'

const props = defineProps<{
  modalName: string
}>()

const emit = defineEmits<{
  success: []
}>()

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

const modalForm = createProModalForm<Partial<Entity.DictType>>({
  omitEmptyString: false,
  initialValues: {
    dictName: '',
    dictType: '',
    status: '0',
    remark: '',
  },
  onSubmit: handleSubmit,
})

type ModalType = 'add' | 'edit'
const modalType = shallowRef<ModalType>('add')
const modalTitle = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '添加',
    edit: '编辑',
  }
  return `${titleMap[modalType.value]}${props.modalName}`
})

// 提交表单
async function handleSubmit(filedValues: Partial<Entity.DictType>) {
  try {
    startLoading()

    if (modalType.value === 'add') {
      await addType(filedValues)
      window.$message.success('新增字典类型成功')
    }
    else {
      await updateType({
        ...filedValues,
        dictId: modalForm.values.value.dictId,
      })
      window.$message.success('修改字典类型成功')
    }

    modalForm.close()
    emit('success')
  }
  catch (error) {
    console.error('提交失败:', error)
    window.$message.error('提交失败')
  }
  finally {
    endLoading()
  }
}

// 打开弹窗
async function openModal(type: ModalType, data?: Entity.DictType) {
  modalType.value = type
  modalForm.open()

  const handlers = {
    async add() {

    },
    async edit() {
      if (!data)
        return
      // 获取完整的字典类型信息
      const response = await getType(data.dictId!)
      modalForm.values.value = response.data
    },
  }
  await handlers[type]()
}

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :title="modalTitle"
    :loading="loading"
  >
    <n-grid cols="1" x-gap="16">
      <n-gi>
        <pro-input title="字典名称" path="dictName" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-input title="字典类型" path="dictType" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-radio-group
          title="状态"
          path="status"
          :field-props="{
            options: [
              { label: '正常', value: '0' },
              { label: '停用', value: '1' },
            ],
          }"
        />
      </n-gi>

      <n-gi>
        <pro-textarea
          title="备注"
          path="remark"
          :field-props="{
            placeholder: '请输入内容',
            rows: 3,
          }"
        />
      </n-gi>
    </n-grid>
  </pro-modal-form>
</template>

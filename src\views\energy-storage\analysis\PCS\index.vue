<script setup lang="ts">
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
import Voltage from './components/Voltage.vue'
import Power from './components/Power.vue'
</script>

<template>
  <EnergyStorageBackground class="h-full">
    <BHeader title="分布式储能运营云平台" />
    <main class=" h-[calc(100%-80px)] grid grid-rows-[auto_1fr_1fr] gap-3 p-3">
      <div class="flex gap-3">
        <n-select class="w-300px" />
        <n-select class="w-300px" />
        <n-date-picker type="daterange" class="ml-auto" />
        <n-button type="primary">
          查询
        </n-button>
      </div>

      <Voltage />
      <Power />
    </main>
  </EnergyStorageBackground>
</template>

<script setup lang="ts">
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import { usePowerOptions } from '../echarts-options/StationOperationStatus'

const powerOptions = usePowerOptions()
useEcharts('echartsRef', powerOptions)
</script>

<template>
  <BCard title="电站运行状态">
    <template #extend>
      <n-date-picker type="daterange" size="small" />
    </template>
    <div ref="echartsRef" class="w-full h-full" />
  </BCard>
</template>

<style scoped>

</style>

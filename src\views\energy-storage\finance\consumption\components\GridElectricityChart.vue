<script setup lang="ts">
import { computed, ref } from 'vue'
import { useThemeVars } from 'naive-ui'
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import type { ECOption } from '@/hooks/useEcharts'

const activeTab = ref('today')
const themeVars = useThemeVars()

const chartOptions = computed<ECOption>(() => {
  // 根据选中的标签生成不同的数据
  let times, consumption, cost

  switch (activeTab.value) {
    case 'today':
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      consumption = [40, 35, 60, 80, 45, 70, 55]
      cost = [10, 8, 15, 20, 12, 18, 14]
      break
    case 'month':
      times = ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
      consumption = [1200, 1350, 1100, 1400, 1300, 1250, 1200]
      cost = [300, 340, 280, 350, 325, 310, 300]
      break
    case 'year':
      times = ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
      consumption = [36000, 34000, 38000, 35000, 37000, 36000, 35000]
      cost = [9000, 8500, 9500, 8750, 9250, 9000, 8750]
      break
    default:
      times = ['01:30', '01:31', '01:32', '01:33', '01:34', '01:35', '01:36']
      consumption = [40, 35, 60, 80, 45, 70, 55]
      cost = [10, 8, 15, 20, 12, 18, 14]
  }

  // 定义统一的颜色
  const electricityColor = 'rgba(81,168,246,0.7)' // #51a8f6 蓝色
  const costColor = 'rgba(83,206,142,0.7)' // #53ce8e 绿色

  // 构建系列数据 - 只创建两个系列
  const series: any[] = [
    {
      name: '用电量(kwh)',
      type: 'pictorialBar',
      smooth: false,
      legendHoverLink: false,
      symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
      barGap: '30%', // 设置柱状图之间的间隔
      barWidth: '30%', // 设置柱状图宽度，稍微减小一点
      label: {
        show: false,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: electricityColor },
            { offset: 1, color: electricityColor },
          ],
          globalCoord: false,
        },
      },
      data: consumption,
    },
    {
      name: '费用(元)',
      type: 'pictorialBar',
      smooth: false,
      legendHoverLink: false,
      symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
      barGap: '30%', // 设置柱状图之间的间隔
      barWidth: '30%', // 设置柱状图宽度，稍微减小一点
      label: {
        show: false,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: costColor },
            { offset: 1, color: costColor },
          ],
          globalCoord: false,
        },
      },
      data: cost,
    },
  ]

  return {
    color: ['#51a8f6', '#53ce8e'],
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        let rValue = `${params[0].name}<br>`
        params.forEach((v: any) => {
          if (v.data !== 0 && v.data !== '-' && v.seriesType === 'pictorialBar') {
            const seriesName = v.seriesName.includes('-费用') ? '费用(元)' : '用电量(kwh)'
            rValue += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${v.color}"></span>${seriesName}: ${v.data}<br>`
          }
        })
        return rValue
      },
    },
    legend: {
      icon: 'circle',
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 15,
      data: ['用电量(kwh)', '费用(元)'],
      right: '4%',
      textStyle: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: true, // 改为 true，在两端留出空间
      data: times,
      axisLabel: {
        fontSize: 14,
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '单位: kwh',
      nameTextStyle: {
        color: themeVars.value.textColor1,
      },
      axisLabel: {
        color: themeVars.value.textColor1,
      },
      axisLine: {
        lineStyle: {
          color: themeVars.value.textColor1,
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeVars.value.borderColor,
        },
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    series,
  }
})

useEcharts('gridChart', chartOptions)
</script>

<template>
  <BCard title="电网电量/费用">
    <template #extend>
      <NRadioGroup v-model:value="activeTab" size="small" type="line">
        <NRadioButton value="today" label="今日" />
        <NRadioButton value="month" label="本月" />
        <NRadioButton value="year" label="本年" />
      </NRadioGroup>
    </template>
    <div ref="gridChart" class="w-full h-full" />
  </BCard>
</template>

<style scoped>
:deep(.n-card__content) {
  height: calc(100% - 50px);
}
</style>

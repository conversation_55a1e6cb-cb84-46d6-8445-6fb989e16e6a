<script setup lang="ts">
import { useBoolean } from '@/hooks'
import { dataScope, roleDeptTreeSelect } from '@/service/api/system/role'

const emit = defineEmits<{
  success: []
}>()

const { bool: modalVisible, setTrue: showModal, setFalse: hiddenModal } = useBoolean(false)
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// 表单数据
const formModel = ref<Partial<Entity.Role>>({
  roleId: undefined,
  roleName: '',
  roleKey: '',
  dataScope: '1',
  deptIds: [],
  deptCheckStrictly: true,
})

// 部门树数据
const deptTreeData = ref<Api.TreeNode[]>([])
const checkedDeptKeys = ref<number[]>([])
const expandedDeptKeys = ref<number[]>([])

// 数据权限选项
const dataScopeOptions = [
  { label: '全部数据权限', value: '1' },
  { label: '自定数据权限', value: '2' },
  { label: '本部门数据权限', value: '3' },
  { label: '本部门及以下数据权限', value: '4' },
  { label: '仅本人数据权限', value: '5' },
]

// 获取部门树数据
async function getDeptTreeData(roleId?: number) {
  try {
    if (roleId) {
      // 编辑时获取角色的部门权限
      const response = await roleDeptTreeSelect(roleId)
      deptTreeData.value = response.depts || []
      checkedDeptKeys.value = response.checkedKeys || []
      // 默认展开所有节点
      expandedDeptKeys.value = response.depts?.map((item: Api.TreeNode) => item.id) || []
    }
    else {
      // 新增时获取所有部门
      const response = await roleDeptTreeSelect(0)
      deptTreeData.value = response.depts || []
      checkedDeptKeys.value = []
      // 默认展开所有节点
      expandedDeptKeys.value = response.depts?.map((item: Api.TreeNode) => item.id) || []
    }
  }
  catch (error) {
    console.error('获取部门树失败:', error)
    deptTreeData.value = []
    checkedDeptKeys.value = []
  }
}

// 展开/折叠所有部门节点
function handleDeptExpandToggle() {
  expandedDeptKeys.value = expandedDeptKeys.value.length > 0 ? [] : getAllTreeNodeIds(deptTreeData.value)
}

// 全选/全不选部门节点
function handleDeptSelectAll() {
  checkedDeptKeys.value = checkedDeptKeys.value.length > 0 ? [] : getAllTreeNodeIds(deptTreeData.value)
}

// 处理部门父子联动状态变化
function handleDeptLinkageChange(checked: boolean) {
  formModel.value.deptCheckStrictly = checked
}

// 获取所有树节点ID
function getAllTreeNodeIds(nodes: Api.TreeNode[]): number[] {
  const ids: number[] = []
  function traverse(items: Api.TreeNode[]) {
    items.forEach((item) => {
      ids.push(item.id)
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }
  traverse(nodes)
  return ids
}

// 打开弹窗
async function openModal(roleData: Entity.Role) {
  showModal()

  // 设置表单数据
  formModel.value = {
    roleId: roleData.roleId,
    roleName: roleData.roleName,
    roleKey: roleData.roleKey,
    dataScope: roleData.dataScope,
    deptIds: roleData.deptIds || [],
    deptCheckStrictly: roleData.deptCheckStrictly ?? true,
  }

  // 获取部门树数据（传递角色ID以获取已选部门）
  await getDeptTreeData(roleData.roleId)
}

// 关闭弹窗
function closeModal() {
  hiddenModal()
  endLoading()
}

// 提交表单
async function handleSubmit() {
  try {
    startLoading()

    const dataScopeParams = {
      roleId: formModel.value.roleId!,
      roleName: formModel.value.roleName!,
      roleKey: formModel.value.roleKey!,
      dataScope: formModel.value.dataScope!,
      deptIds: formModel.value.dataScope === '2' ? checkedDeptKeys.value : [],
      deptCheckStrictly: formModel.value.deptCheckStrictly!,
    }

    await dataScope(dataScopeParams)
    window.$message.success('数据权限分配成功')
    emit('success')
    closeModal()
  }
  catch (error) {
    console.error('数据权限分配失败:', error)
    window.$message.error('数据权限分配失败')
  }
  finally {
    endLoading()
  }
}

// 监听数据权限变化
watch(() => formModel.value.dataScope, (newValue) => {
  if (newValue !== '2') {
    // 非自定数据权限时清空部门选择
    checkedDeptKeys.value = []
  }
})

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    preset="card"
    title="分配数据权限"
    class="w-600px"
    :segmented="{
      content: true,
      action: true,
    }"
  >
    <n-form label-placement="left" :model="formModel" :label-width="80" size="small">
      <n-form-item label="角色名称">
        {{ formModel.roleName }}
      </n-form-item>

      <n-form-item label="权限字符">
        {{ formModel.roleKey }}
      </n-form-item>

      <n-form-item label="权限范围">
        <n-select
          v-model:value="formModel.dataScope"
          :options="dataScopeOptions"
          placeholder="请选择权限范围"
        />
      </n-form-item>

      <n-form-item v-if="formModel.dataScope === '2'" label="数据权限">
        <n-space vertical class="w-full">
          <n-space>
            <n-checkbox @click="handleDeptExpandToggle">
              展开/折叠
            </n-checkbox>
            <n-checkbox @click="handleDeptSelectAll">
              全选/全不选
            </n-checkbox>
            <n-checkbox
              :checked="formModel.deptCheckStrictly"
              @update:checked="handleDeptLinkageChange"
            >
              父子联动
            </n-checkbox>
          </n-space>
          <n-scrollbar style="border: 1px solid var(--n-border-color)" class="max-h-300px rd">
            <n-tree
              v-model:checked-keys="checkedDeptKeys"
              v-model:expanded-keys="expandedDeptKeys"
              :data="deptTreeData"
              key-field="id"
              label-field="label"
              children-field="children"
              checkable
              :cascade="formModel.deptCheckStrictly"
              :check-strategy="formModel.deptCheckStrictly ? 'all' : 'child'"
            />
          </n-scrollbar>
        </n-space>
      </n-form-item>
    </n-form>

    <template #action>
      <n-space justify="end">
        <n-button @click="closeModal">
          取消
        </n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

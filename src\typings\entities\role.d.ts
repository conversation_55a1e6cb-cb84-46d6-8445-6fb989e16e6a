/// <reference path="../global.d.ts"/>

/** 系统角色相关实体定义 */
namespace Entity {
  /** 角色数据库表字段 */
  interface Role {
    /** 创建者 */
    createBy: any
    /** 创建时间 */
    createTime: string
    /** 更新者 */
    updateBy: any
    /** 更新时间 */
    updateTime: any
    /** 备注 */
    remark: any
    /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） */
    dataScope: string
    /** 角色ID */
    roleId: number
    /** 角色名称 */
    roleName: string
    /** 角色权限字符串 */
    roleKey: string
    /** 显示顺序 */
    roleSort: number
    /** 菜单树选择项是否关联显示 */
    menuCheckStrictly: boolean
    /** 部门树选择项是否关联显示 */
    deptCheckStrictly: boolean
    /** 角色状态（0正常 1停用） */
    status: '0' | '1'
    /** 删除标志（0代表存在 2代表删除） */
    delFlag: string
    /** 用户是否存在此角色标识 默认不存在 */
    flag: boolean
    /** 菜单组 */
    menuIds: any
    /** 部门组（数据权限） */
    deptIds: any
    /** 权限组 */
    permissions: any
    /** 管理员标识 */
    admin: boolean
  }
}

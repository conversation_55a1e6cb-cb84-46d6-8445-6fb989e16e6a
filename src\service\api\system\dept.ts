import { request } from '../../http'

// 部门查询参数类型
export interface DeptQueryParams {
  deptName?: string
  status?: '0' | '1'
  parentId?: number
}

// 查询部门列表
export function listDept(params?: DeptQueryParams) {
  return request.Get<Api.ResponseWithData<Entity.Dept[]>>('/system/dept/list', { params })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId: number) {
  return request.Get<Api.ResponseWithData<Entity.Dept[]>>(`/system/dept/list/exclude/${deptId}`)
}

// 查询部门详细
export function getDept(deptId: number) {
  return request.Get<Api.ResponseWithData<Entity.Dept>>(`/system/dept/${deptId}`)
}

// 新增部门
export function addDept(data: Partial<Entity.Dept>) {
  return request.Post<Api.BaseResponse>('/system/dept', data)
}

// 修改部门
export function updateDept(data: Partial<Entity.Dept>) {
  return request.Put<Api.BaseResponse>('/system/dept', data)
}

// 删除部门
export function delDept(deptId: number) {
  return request.Delete<Api.BaseResponse>(`/system/dept/${deptId}`)
}

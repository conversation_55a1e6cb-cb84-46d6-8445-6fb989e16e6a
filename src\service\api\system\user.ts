import { request } from '../../http'

// 查询参数类型
export interface UserQueryParams {
  userName?: string
  phonenumber?: string
  status?: '0' | '1'
  params?: {
    beginTime?: string
    endTime?: string
  }
  deptId?: number
  pageNum?: number
  pageSize?: number
}

// 密码重置参数
export interface ResetPwdParams {
  userId: number
  password: string
}

// 状态修改参数
export interface ChangeStatusParams {
  userId: number
  status: '0' | '1'
}

// 修改密码参数
export interface UpdatePwdParams {
  oldPassword: string
  newPassword: string
}

// 查询用户列表
export function listUser(params?: UserQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.UserInfo[]>>('/system/user/list', { params })
}

// 用户详细信息响应类型
export interface UserDetailResponse extends Api.BaseResponse {
  data: Entity.UserInfo
  roleIds: number[]
  postIds: number[]
  roles: Entity.Role[]
  posts: any[]
}

// 查询用户详细
export function getUser(userId?: number) {
  return request.Get<UserDetailResponse>(`/system/user/${userId || ''}`)
}

// 新增用户
export function addUser(data: Partial<Entity.UserInfo>) {
  return request.Post<Api.BaseResponse>('/system/user', data)
}

// 修改用户
export function updateUser(data: Partial<Entity.UserInfo>) {
  return request.Put<Api.BaseResponse>('/system/user', data)
}

// 删除用户
export function delUser(userId: string | number) {
  return request.Delete<Api.BaseResponse>(`/system/user/${userId}`)
}

// 用户密码重置
export function resetUserPwd(params: ResetPwdParams) {
  return request.Put<Api.BaseResponse>('/system/user/resetPwd', params)
}

// 用户状态修改
export function changeUserStatus(params: ChangeStatusParams) {
  return request.Put<Api.BaseResponse>('/system/user/changeStatus', params)
}

// 查询用户个人信息
export function getUserProfile() {
  return request.Get<Api.ResponseWithData<Entity.UserInfo>>('/system/user/profile')
}

// 修改用户个人信息
export function updateUserProfile(data: Partial<Entity.UserInfo>) {
  return request.Put<Api.BaseResponse>('/system/user/profile', data)
}

// 用户密码重置
export function updateUserPwd(params: UpdatePwdParams) {
  return request.Put<Api.BaseResponse>('/system/user/profile/updatePwd', { params })
}

// 用户头像上传
export function uploadAvatar(data: FormData) {
  return request.Post<Api.ResponseWithData<{ imgUrl: string }>>('/system/user/profile/avatar', data)
}

// 批量删除用户
export function delUsers(userIds: string) {
  return request.Delete<Api.BaseResponse>(`/system/user/${userIds}`)
}

// 导出用户数据
export function exportUser(params?: UserQueryParams) {
  return request.Post<Blob>('/system/user/export', params, {
    meta: { isBlob: true },
  })
}

// 下载用户导入模板
export function importTemplate() {
  return request.Post<Blob>('/system/user/importTemplate', {}, {
    meta: { isBlob: true },
  })
}

// 用户导入
export function importUser(data: FormData, updateSupport: boolean = false) {
  return request.Post<Api.ResponseWithData<string>>(`/system/user/importData?updateSupport=${updateSupport}`, data)
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request.Get<Api.ResponseWithData<Api.TreeNode[]>>('/system/user/deptTree')
}

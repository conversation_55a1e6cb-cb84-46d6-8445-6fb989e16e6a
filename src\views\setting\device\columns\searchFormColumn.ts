import { useDict } from '@/hooks'
import type { ProSearchFormColumns } from 'pro-naive-ui'

export function useSearchFormColumn() {
  const { options: deviceTypeOptions } = useDict('device_type')
  return computed<ProSearchFormColumns>(() => {
    return [
      {
        title: '设备类型',
        path: 'deviceType',
        field: 'select',
        placeholder: '请选择设备类型',
        fieldProps: {
          options: deviceTypeOptions.value,
        },
      },
      {
        title: '设备名称',
        path: 'deviceName',
        field: 'input',
        placeholder: '请输入设备名称',
      },
      {
        title: '设备型号',
        path: 'deviceModel',
        field: 'input',
        placeholder: '请输入设备型号',
      },
      {
        title: '设备单位',
        path: 'deviceUnit',
        field: 'input',
        placeholder: '请输入设备单位',
      },
      {
        title: '设备数量',
        path: 'deviceNum',
        field: 'input',
        placeholder: '请输入设备数量',
      },
      {
        title: '生产厂家',
        path: 'producer',
        field: 'input',
        placeholder: '请输入生产厂家',
      },
      {
        title: '创建时间',
        path: 'beginTime',
        field: 'date-range',
        placeholder: '请选择创建时间范围',
      },
    ]
  })
}

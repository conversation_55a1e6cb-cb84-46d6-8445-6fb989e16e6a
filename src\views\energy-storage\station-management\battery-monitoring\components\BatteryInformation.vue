<script setup lang="tsx">
import { NProgress, NSpace, NTag, NText } from 'naive-ui'
import type { TagProps } from 'naive-ui'
import ChargingCapacity from '@/assets/imgs/energy-storage/station-management/charging-capacity.png'
import DischargeCapacity from '@/assets/imgs/energy-storage/station-management/discharge-capacity.png'
import type { FunctionalComponent } from 'vue'

const TextWithTag: FunctionalComponent<{ name: string, tag: string, type: TagProps['type'] }> = (props) => {
  return (
    <NSpace
      class="h-[22px]"
      justify="space-between"
      style="background:linear-gradient(to right, #C3E2FF99, #EAF4FD00);"
    >
      <NText class="pl-3 h-[22px] vertical-mid" depth="1">{props.name}</NText>
      <NTag size="small" type={props.type}>{props.tag}</NTag>
    </NSpace>
  )
}

const Temperature: FunctionalComponent<{
  name: string
  temperature: string
  progress: number
  label?: string
}> = (props) => {
  return (
    <div class="w-full">
      <NSpace justify="space-between">
        <NText>{props.name}</NText>
        {props.label && (
          <NText type="warning">
            {props.label}
            号
          </NText>
        )}
        <NText>
          {props.temperature}
          °C
        </NText>
      </NSpace>
      <NProgress
        type="line"
        height={4}
        border-radius={0}
        percentage={props.progress}
        show-indicator={false}
        color={{ stops: ['transparent', 'var(--primary-color)'] }}
      />
    </div>
  )
}

const Voltage: FunctionalComponent<{
  name: string
  temperature: string
  label?: string
}> = (props) => {
  return (
    <div class="w-full">
      <NSpace justify="space-between">
        <NText>{props.name}</NText>
        {props.label && (
          <NText type="success">
            {props.label}
            号
          </NText>
        )}
        <NText>
          {props.temperature}
          V
        </NText>
      </NSpace>
    </div>
  )
}

const QuantityOfElectricity: FunctionalComponent<{ icon: string, name: string, value: number }> = (props) => {
  return (
    <div class="flex justify-between items-center">
      <img class="w-[60px] h-auto" src={props.icon} alt="icon" />
      <NText>{props.name}</NText>
      <NText type="info" strong>{props.value}</NText>
      <NText>kwh</NText>
    </div>
  )
}
</script>

<template>
  <BCard class="batteryInformation" title="电池信息">
    <template #extend>
      <n-select class="w-[120px]" placeholder="场站名称" size="small" />
      <n-select class="w-[120px]" placeholder="1#BMS" size="small" />
    </template>
    <header
      class="status bg-size-[100% 100%] bg-no-repeat h-[130px] w-[530px] mx-auto grid grid-rows-2 grid-cols-2 justify-between items-center self-auto"
    >
      <div class="text-left">
        <n-p class="m-0" depth="2">
          电池电压
        </n-p>
        <n-p class="m-0" depth="1">
          xxx v
        </n-p>
      </div>
      <div class="text-right">
        <n-p class="m-0" depth="2">
          电池电流
        </n-p>
        <n-p class="m-0" depth="1">
          xxx A
        </n-p>
      </div>
      <div>
        <n-p class="m-0" depth="2">
          电池SOC
        </n-p>
        <n-p class="m-0" depth="1">
          xxx %
        </n-p>
      </div>
      <div class="text-right">
        <n-p class="m-0" depth="2">
          电池SOH
        </n-p>
        <n-p class="m-0" depth="1">
          xxx %
        </n-p>
      </div>
    </header>
    <main class="information mt-4">
      <div>
        <img src="@/assets/imgs/energy-storage/station-management/individual-temperature.png" alt="icon">
        <div>
          <TextWithTag name="单体温度" tag="异常" type="warning" />
          <Temperature name="单体平均温度" temperature="36" :progress="20" />
          <Temperature name="单体最高温度" temperature="35" label="3" :progress="20" />
          <Temperature name="单体最低温度" temperature="34" label="5" :progress="20" />
        </div>
      </div>
      <div>
        <img src="@/assets/imgs/energy-storage/station-management/individual-voltage.png" alt="icon">
        <div>
          <TextWithTag name="单体电压" tag="正常" type="success" />
          <Voltage name="单体平均电压" temperature="36" />
          <Voltage name="单体最高电压" temperature="35" label="3" />
          <Voltage name="单体最低电压" temperature="34" label="5" />
        </div>
      </div>
      <QuantityOfElectricity :icon="ChargingCapacity" name="累计充电量" :value="0.13" />
      <QuantityOfElectricity :icon="DischargeCapacity" name="累计放电量" :value="3.20" />
    </main>
  </BCard>
</template>

<style lang="scss" scoped>
.batteryInformation {
  .status {
    background-image: url("@/assets/imgs/energy-storage/station-management/battery-information-background.png"), url("@/assets/imgs/energy-storage/station-management/battery-fully-charged.png");
    background-size: 100% 100%, 36px 68px;
    background-repeat: no-repeat;
    background-position: center;
  }

  .information {
    display: grid;
    grid-template-rows: 3fr 1fr;
    grid-template-columns: 1fr 1fr;
    height: 250px;
    gap: 18px;
    padding: 10px;

    & > div {
      border: 1px solid #84BDF2;
      padding: 10px;
    }

    & > div:nth-child(1), & > div:nth-child(2) {

      display: flex;
      gap: 10px;

      img {
        width: 80px;
        object-fit: contain;
        height: auto;
        margin: 5px;
        user-select: none;
        -webkit-user-drag: none;

      }

      & > div {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px 0 10px 0;
        gap: 5px;
      }

    }
  }
}
</style>

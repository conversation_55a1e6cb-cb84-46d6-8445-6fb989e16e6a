/**
 * 配置接口
 */
interface TreeConfig {
  id: string
  parentId: string
  childrenList: string
}

/**
 * 构造树型结构数据
 * @param data 数据源
 * @param id id字段 默认 'id'
 * @param parentId 父节点字段 默认 'pid'
 * @param children 孩子节点字段 默认 'children'
 * @returns 树型结构数据
 */
export function arrayToTree<T extends Record<string, any>>(
  data: T[],
  id: string = 'id',
  parentId: string = 'pid',
  children: string = 'children',
): T[] {
  if (!Array.isArray(data)) {
    console.error('arrayToTree: data must be an array')
    return []
  }

  const config: TreeConfig = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children',
  }

  const childrenListMap: Record<string | number, T[]> = {}
  const nodeIds: Record<string | number, T> = {}
  const tree: T[] = []

  // 构建映射关系
  for (const d of data) {
    const parentIdValue = d[config.parentId]
    if (childrenListMap[parentIdValue] == null) {
      childrenListMap[parentIdValue] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentIdValue].push(d)
  }

  // 找出根节点
  for (const d of data) {
    const parentIdValue = d[config.parentId]
    if (nodeIds[parentIdValue] == null) {
      tree.push(d)
    }
  }

  // 为每个节点添加子节点
  for (const t of tree) {
    adaptToChildrenList(t)
  }

  function adaptToChildrenList(node: T): void {
    const nodeId = node[config.id]
    if (childrenListMap[nodeId] != null) {
      // 使用类型断言来允许写入操作
      (node as Record<string, any>)[config.childrenList] = childrenListMap[nodeId]
    }
    if (node[config.childrenList] && Array.isArray(node[config.childrenList])) {
      for (const child of node[config.childrenList]) {
        adaptToChildrenList(child)
      }
    }
  }

  return tree
}

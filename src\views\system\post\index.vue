<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import { delPost, listPost, updatePost } from '@/service/api/system/post'
import {
  createPostColumns,
  postSearchColumns,
} from './columns'
import type { PostSearchFormData } from './columns'

// 导入子组件
import PostForm from './components/PostForm.vue'

// 弹窗引用
const postFormRef = ref()

// 创建ProSearchForm
const searchForm = createProSearchForm<PostSearchFormData>({
  initialValues: {
    postCode: '',
    postName: '',
    status: undefined,
  },
})

// 使用useNDataTable
const {
  table: {
    tableProps,
  },
  search: {
    proSearchFormProps,
    submit,
    searchLoading,
  },
} = useNDataTable(getList, {
  form: searchForm,
})

// 批量删除相关
const checkedPostIds = ref<number[]>([])

/** 查询岗位列表 */
async function getList({ current, pageSize }: any, formData: PostSearchFormData) {
  try {
    const params = {
      pageNum: current,
      pageSize,
      ...formData, // 使用表单值作为查询条件
    }
    return listPost(params).then(res => ({
      total: res.total,
      list: res.rows,
    }))
  }
  catch (error) {
    console.error('获取岗位列表失败:', error)
    return {
      total: 0,
      list: [],
    }
  }
}

/** 处理状态切换 */
async function handleStatusChange(row: Entity.Post, status: '0' | '1') {
  try {
    await updatePost({
      ...row,
      status,
    })
    window.$message.success('状态更新成功')
    submit()
  }
  catch (error) {
    console.error('状态更新失败:', error)
    window.$message.error('状态更新失败')
  }
}

// 使用提取的岗位表格列配置
const postColumns = createPostColumns({
  onEdit: handleUpdate,
  onDelete: handleDelete,
  onStatusChange: handleStatusChange,
})

/** 新增按钮操作 */
function handleAdd() {
  postFormRef.value?.openModal('add')
}

/** 修改按钮操作 */
async function handleUpdate(row: Entity.Post) {
  postFormRef.value?.openModal('edit', row)
}

/** 删除岗位 */
async function handleDelete(postId: number | number[]) {
  const isBatch = Array.isArray(postId)

  window.$dialog.warning({
    title: '确认删除',
    content: isBatch
      ? `是否确认删除选中的 ${postId.length} 个岗位？`
      : '是否确认删除该岗位？',
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await delPost(postId.toString())
        window.$message.success('删除成功')
        // 清空选中项
        if (isBatch) {
          checkedPostIds.value = []
        }
        submit()
      }
      catch (error) {
        console.error('删除岗位失败:', error)
      }
    },
  })
}
</script>

<template>
  <div>
    <pro-search-form
      v-bind="proSearchFormProps"
      :form="searchForm"
      :columns="postSearchColumns"
      :collapse-button-props="false"
    />
    <!-- 数据表格 -->
    <pro-data-table
      v-bind="tableProps"
      v-model:checked-row-keys="checkedPostIds"
      :columns="postColumns"
      :loading="searchLoading"
      row-key="postId"
    >
      <template #title>
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <icon-park-outline-plus />
          </template>
          新增
        </n-button>
      </template>
      <template #toolbar>
        <n-flex>
          <n-button
            type="error"
            :disabled="checkedPostIds.length === 0"
            @click="handleDelete(checkedPostIds)"
          >
            <template #icon>
              <icon-park-outline-delete />
            </template>
            删除
          </n-button>
        </n-flex>
      </template>
    </pro-data-table>

    <!-- 添加或修改岗位对话框 -->
    <PostForm
      ref="postFormRef"
      modal-name="岗位"
      @success="submit"
    />
  </div>
</template>

<script setup lang="ts">
import { useVue<PERSON>low, VueFlow } from '@vue-flow/core'
import useDragAndDrop from '../hooks/useDnD'
import { ControlButton, Controls } from '@vue-flow/controls'
// these components are only shown as examples of how to use a custom node or edge
// you can find many examples of how to create these custom components in the examples page of the docs
import DropzoneBackground from '@/views/setting/site/editor/components/DropzoneBackground.vue'
import { nodeTypes } from '@/views/setting/site/editor/nodes'
import { useThemeVars } from 'naive-ui'
import { useStore } from '@/views/setting/site/editor/useStore'
import { useToggleToolbar } from '@/views/setting/site/editor/hooks/useToggleToolbar'
import { topologyMapId, useControl } from '@/views/setting/site/editor/hooks/useControl'
import AlignControls from '@/views/setting/site/editor/components/AlignControls.vue'
import HelperLines from '@/views/setting/site/editor/components/HelperLines.vue'
import { useHelperLines } from '@/views/setting/site/editor/hooks/useHelperLines'
import PropertiesEditDrawer from '@/views/setting/site/editor/components/PropertiesEditDrawer.vue'
import { DefaultEdgeType, edgeTypes } from '@/views/setting/site/editor/edges'
import { ValidationStatus } from '@/views/setting/site/editor/types/NodeData'

const { onDragOver, onDrop, onDragLeave, isDragOver } = useDragAndDrop()

const themeVars = useThemeVars()

const { onConnect, addEdges, nodes, addSelectedNodes, setCenter, toObject, fromObject } = useVueFlow(topologyMapId)

const { onNodesChange, helperLineHorizontal, helperLineVertical } = useHelperLines()

useToggleToolbar()

const { save, reset, clear } = useControl()

const store = useStore()

onConnect((connection) => {
  addEdges(connection)
})

function onNext() {
  for (const node of toValue(nodes)) {
    if (node.data.validationStatus !== ValidationStatus.Passed) {
      addSelectedNodes([node])

      setCenter(node.position.x, node.position.y)

      window.$message.warning('请先完成所有节点的配置')
      return
    }
  }

  store.step = 3 // next
}

onBeforeUnmount(() => {
  store.basicInfo.siteTopologicalMap = toObject()
})
onMounted(() => {
  fromObject(store.basicInfo.siteTopologicalMap)
})
</script>

<template>
  <VueFlow
    :id="topologyMapId"
    :node-types="nodeTypes"
    :edge-types="edgeTypes"
    :default-edge-options="{ type: DefaultEdgeType, style: { stroke: themeVars.primaryColor, strokeWidth: '3px' }, data: { payload: {} } }"
    :connection-line-options="{ type: DefaultEdgeType, style: { stroke: themeVars.primaryColor, strokeWidth: '3px' } }"
    @dragover="onDragOver" @dragleave="onDragLeave"
    @drop="onDrop"
    @nodes-change="onNodesChange"
  >
    <AlignControls />
    <HelperLines :horizontal="helperLineHorizontal" :vertical="helperLineVertical" />
    <PropertiesEditDrawer />
    <Controls position="top-left">
      <ControlButton @click="save">
        <NovaIcon class="text-[12px]!" icon="carbon:save" />
      </ControlButton>
      <ControlButton @click="reset">
        <NovaIcon class="text-[12px]!" icon="carbon:reset" />
      </ControlButton>
      <ControlButton @click="clear">
        <NovaIcon class="text-[12px]!" icon="carbon:clean" />
      </ControlButton>
    </Controls>
    <DropzoneBackground
      class="wh-full flex justify-center items-center"
      :style="{
        backgroundColor: isDragOver ? `${themeVars.primaryColor}55` : 'transparent',
        transition: 'background-color 0.2s ease',
      }"
    >
      <p v-if="isDragOver">
        放在这里
      </p>
    </DropzoneBackground>

    <footer class="absolute bottom-0 right-0 flex gap-3 z-10">
      <NButton type="primary" @click="store.step = 1">
        上一步
      </NButton>
      <NButton type="primary" @click="onNext">
        生成资产清单
      </NButton>
    </footer>
  </VueFlow>
</template>

<style>
/* these are necessary styles for vue flow */
@import '@vue-flow/core/dist/style.css';

/* this contains the default theme, these are optional styles */
@import '@vue-flow/core/dist/theme-default.css';

/* import default controls styles */
@import '@vue-flow/controls/dist/style.css';
</style>

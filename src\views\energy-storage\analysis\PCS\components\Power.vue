<script setup lang="ts">
import type { ECOption } from '@/hooks'
import { useEcharts } from '@/hooks'
import dayjs from '@/utils/dayjs'

const dataLength = 100

const xLabel = Array.from({ length: dataLength }, (_, i) => dayjs().add(i, 'hour').format('YYYY-MM-DD HH:mm')).reverse()

const data = [
  Array.from({ length: dataLength }, () => (Math.random() * 10).toFixed(2)),
  Array.from({ length: dataLength }, () => (Math.random() * 10).toFixed(2)),
  Array.from({ length: dataLength }, () => (Math.random() * 10).toFixed(2)),
]
const chatOptions = computed<ECOption>(() => {
  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName} ${param.value}Kw<br/>`
        })
        return result
      },
    },
    grid: {
      left: '2%',
      right: '2%',
      bottom: '50',
      top: '10%',
      containLabel: true,
    },
    dataZoom: [
      {
        show: true,
        start: 0,
        end: 100,
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xLabel,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#CFE6FC',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
      },
    },
    series: [
      {
        color: '#F7C065',
        name: '交流现在',
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0.25,
                color: 'rgba(247, 192, 101, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(247, 192, 101, 0)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
        data: data[0],
      },
      {
        color: '#37a2da',
        name: '交流无功',
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(55, 162, 218, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(55, 162, 218, 0)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
        data: data[1],
      },
      {
        color: '#61C26A',
        name: '交流有功',
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0.25,
                color: 'rgba(159, 230, 184, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(159, 230, 184, 0)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
        data: data[2],
      },

    ],
    legend: {},
  }
})

useEcharts('echarts', chatOptions)
</script>

<template>
  <BCard title="功率">
    <div ref="echarts" class="h-full" />
  </BCard>
</template>

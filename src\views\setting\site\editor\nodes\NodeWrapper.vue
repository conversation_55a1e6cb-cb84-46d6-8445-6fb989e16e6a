<script setup lang="tsx">
import type { NodeProps } from '@vue-flow/core'
import type { NodeData } from '@/views/setting/site/editor/types/NodeData'
import { ValidationStatus } from '@/views/setting/site/editor/types/NodeData'
import NovaIcon from '@/components/common/NovaIcon.vue'

const props = defineProps<NodeProps<NodeData<any>>>()

const Tag = computed(() => {
  switch (props.data.validationStatus) {
    case ValidationStatus.Passed:
      return () => <NovaIcon color="var(--success-color)" icon="carbon:checkmark-outline"></NovaIcon>
    case ValidationStatus.Failed:
      return () => <NovaIcon color="var(--error-color)" icon="carbon:error"></NovaIcon>
    case ValidationStatus.Pending:
    default:
      return () => <NovaIcon color="var(--warning-color)" icon="carbon:information"></NovaIcon>
  }
})

const tooltip = computed(() => ({
  [ValidationStatus.Pending]: '请补充节点属性',
  [ValidationStatus.Failed]: '节点校验失败，请检查属性',
  [ValidationStatus.Passed]: '节点属性校验通过',
})[props.data.validationStatus])
</script>

<template>
  <div
    class="custom-node-wrapper relative"
    :class="{ selected }"
  >
    <n-tooltip placement="top-start">
      <template #trigger>
        <Tag class="absolute right-0 top-0 cursor-pointer" />
      </template>
      {{ tooltip }}
    </n-tooltip>
    <!--    <NodeToolbar -->
    <!--      class="bg-white min-w-[200px] p-3 border-[var(&#45;&#45;border-color)] border rounded" -->
    <!--      :is-visible="data.toolbarVisible" -->
    <!--      :position="data.toolbarPosition" -->
    <!--    > -->
    <!--      <header> -->
    <!--        <n-h3 class="text-center"> -->
    <!--          <n-text type="info"> -->
    <!--            {{ data.meta.name }}属性 -->
    <!--          </n-text> -->
    <!--        </n-h3> -->
    <!--      </header> -->
    <!--      <main class="grid grid-cols-2 gap-3"> -->
    <!--        <n-input v-model:value="data.payload.manufacturer" placeholder="请输入生产厂家" /> -->
    <!--        <n-input v-model:value="data.payload.model" placeholder="请输入设备型号" /> -->
    <!--        <n-input v-model:value="data.payload.unit" placeholder="请输入单位" /> -->
    <!--        <n-input v-model:value="data.payload.remark" placeholder="请输入备注" /> -->
    <!--      </main> -->
    <!--    </NodeToolbar> -->
    <slot :props="$props" />
  </div>
</template>

<style scoped>
.custom-node-wrapper {
  position: relative;
  background: var(--body-color);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  min-width: 120px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.custom-node-wrapper:hover {
  border-color: var(--primary-color-hover);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.custom-node-wrapper.selected {
  border-color: var(--primary-color-pressed);
  box-shadow: 0 0 0 2px rgba(29, 78, 216, 0.2);
}

/* Handle 样式 */
:deep(.vue-flow__handle-top) {
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: #3b82f6;
  border: 2px solid white;
  width: 12px;
  height: 12px;
}

:deep(.vue-flow__handle-bottom) {
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: #10b981;
  border: 2px solid white;
  width: 12px;
  height: 12px;
}

:deep(.vue-flow__handle-left) {
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  background: #f59e0b;
  border: 2px solid white;
  width: 12px;
  height: 12px;
}

:deep(.vue-flow__handle-right) {
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  background: #ef4444;
  border: 2px solid white;
  width: 12px;
  height: 12px;
}

/* Handle 悬停效果 */
:deep(.vue-flow__handle-top:hover),
:deep(.vue-flow__handle-bottom:hover),
:deep(.vue-flow__handle-left:hover),
:deep(.vue-flow__handle-right:hover) {
  background: #1d4ed8;
  transform: translateX(-50%) scale(1.2);
}

:deep(.vue-flow__handle-left:hover),
:deep(.vue-flow__handle-right:hover) {
  transform: translateY(-50%) scale(1.2);
}
</style>

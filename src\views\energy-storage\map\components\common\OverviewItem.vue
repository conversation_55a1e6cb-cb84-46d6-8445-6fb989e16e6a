<script setup lang="ts">
defineProps<{
  icon: string
  name: string
  value: string
  unit: string
}>()
</script>

<template>
  <div class="overviewItem flex items-center gap-5 h-[58px]">
    <img class="ml-[17px] select-none pointer-events-none" :src="icon" width="42" height="42" alt="icon">
    <div>
      <n-text class="m-0" :depth="1">
        {{ name }}
      </n-text>
      <n-p class="m-0">
        <n-text class="font-bold text-xl" :depth="1">
          {{ value }}
        </n-text>
        <n-text class="text-xs">
          {{ unit }}
        </n-text>
      </n-p>
    </div>
  </div>
</template>

<style scoped>
.overviewItem {
  background-image: url("/src/assets/imgs/energy-storage/background.png");
}
</style>

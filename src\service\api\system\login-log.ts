import { request } from '../../http'

// 登录日志查询参数
export interface LoginLogQueryParams {
  ipaddr?: string
  userName?: string
  status?: '0' | '1'
  params?: {
    beginTime?: string
    endTime?: string
  }
  pageNum?: number
  pageSize?: number
}

// 查询登录日志列表
export function listLoginLog(params?: LoginLogQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.LoginLog[]>>('/monitor/logininfor/list', { params })
}

// 删除登录日志
export function delLoginLog(infoId: string) {
  return request.Delete<Api.BaseResponse>(`/monitor/logininfor/${infoId}`)
}

// 解锁用户登录状态
export function unlockUser(userName: string) {
  return request.Get<Api.BaseResponse>(`/monitor/logininfor/unlock/${userName}`)
}

// 清空登录日志
export function cleanLoginLog() {
  return request.Delete<Api.BaseResponse>('/monitor/logininfor/clean')
}

// 导出登录日志
export function exportLoginLog(params?: LoginLogQueryParams) {
  return request.Post<Blob>('/monitor/logininfor/export', params, {
    meta: { isBlob: true },
  })
}

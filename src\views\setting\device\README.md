# 设备管理

## 功能概述

设备管理模块提供了对系统中设备信息的完整管理功能，包括设备的增删改查操作。

## 主要功能

### 1. 设备列表查询
- 支持分页查询设备列表
- 支持按设备类型、设备名称、设备型号、设备单位、设备数量、生产厂家等条件筛选
- 支持按创建时间范围筛选

### 2. 设备信息管理
- **新增设备**：添加新的设备信息
- **编辑设备**：修改现有设备信息
- **删除设备**：删除设备记录

### 3. 设备字段说明
- `deviceType`: 设备类型（使用字典数据 `device_type`）
- `deviceName`: 设备名称
- `deviceModel`: 设备型号
- `deviceUnit`: 设备单位
- `deviceNum`: 设备数量
- `maintenanceCycle`: 维护周期
- `maintenanceInfo`: 维护信息
- `specification`: 规格
- `producer`: 生产厂家
- `remark`: 备注信息

### 4. 字典数据支持
- 设备类型字段使用字典数据 `device_type`
- 搜索表单中的设备类型下拉框自动加载字典数据
- 表格中的设备类型显示字典标签
- 新增/编辑表单中的设备类型下拉框自动加载字典数据

## API接口

### 查询设备列表
```
GET /base/device/list
```

### 查询设备详情
```
GET /base/device/{deviceId}
```

### 新增设备
```
POST /base/device
```

### 修改设备
```
PUT /base/device
```

### 删除设备
```
DELETE /base/device/{deviceIds}
```

### 导出设备
```
POST /base/device/export
```

### 字典数据接口
```
GET /system/dict/data/type/device_type
```

## 文件结构

```
equipment-management/
├── index.vue                 # 主页面
├── components/               # 组件目录
│   ├── CreateModal.vue      # 新增设备模态框
│   ├── EditModal.vue        # 编辑设备模态框
│   └── ModalContent.vue     # 模态框内容组件
├── columns/                  # 列配置目录
│   ├── tableColumns.ts      # 表格列配置
│   └── searchFormColumn.ts  # 搜索表单列配置
├── types/                    # 类型定义目录
│   ├── Device.ts            # 设备类型定义
│   └── Form.ts              # 表单类型定义
└── README.md                # 说明文档
```

## 使用说明

1. **查看设备列表**：页面加载时自动显示设备列表
2. **搜索设备**：使用搜索表单按条件筛选设备
3. **新增设备**：点击"添加设备"按钮，填写设备信息后保存
4. **编辑设备**：点击设备行的"编辑"按钮，修改信息后保存
5. **删除设备**：点击设备行的"删除"按钮，确认后删除

## 字典数据配置

### 设备类型字典配置
在系统管理中配置字典类型 `device_type`，例如：
- 字典标签: 变压器, 字典值: transformer
- 字典标签: 开关柜, 字典值: switchgear
- 字典标签: 电缆, 字典值: cable
- 字典标签: 其他, 字典值: other

## 数据库字段映射

| 前端字段名 | 数据库字段名 | 说明 |
|-----------|-------------|------|
| deviceType | device_type | 设备类型 |
| deviceName | device_name | 设备名称 |
| deviceModel | device_model | 设备型号 |
| deviceUnit | device_unit | 设备单位 |
| deviceNum | device_num | 设备数量 |
| maintenanceCycle | maintenance_cycle | 维护周期 |
| maintenanceInfo | maintenance_info | 维护信息 |
| specification | specification | 规格 |
| producer | producer | 生产厂家 |

## 注意事项

- 设备数量需要保持唯一性
- 删除操作不可恢复，请谨慎操作
- 所有必填字段都需要填写完整
- 设备类型字段会自动从字典数据中加载选项

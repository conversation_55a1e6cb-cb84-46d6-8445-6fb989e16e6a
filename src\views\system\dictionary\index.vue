<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import {
  delType,
  listType,
  updateType,
} from '@/service/api/system/dict-type'
import { useDictStore } from '@/store'
import {
  createTypeColumns,
  typeSearchColumns,
} from './columns'
import type { DictTypeSearchFormData } from './columns'

// 导入子组件
import DictDataDrawer from './components/DictDataDrawer.vue'
import TypeForm from './components/TypeForm.vue'

// 弹窗引用
const typeFormRef = ref()

// 创建ProSearchForm
const searchForm = createProSearchForm<DictTypeSearchFormData>({
  initialValues: {
    dictName: '',
    dictType: '',
    status: undefined,
  },
})

// 使用useNDataTable
const {
  table: {
    tableProps,
  },
  search: {
    proSearchFormProps,
    searchLoading,
    submit,
  },
} = useNDataTable(getList, {
  form: searchForm,
})

// 右侧抽屉相关
const drawerVisible = ref(false)
const currentDictType = ref<Entity.DictType | null>(null)

// 批量删除相关
const checkedDictTypeIds = ref<number[]>([])

/** 查询字典类型列表 */
async function getList({ current, pageSize }: any, formData: DictTypeSearchFormData) {
  try {
    const params = {
      pageNum: current,
      pageSize,
      ...formData, // 使用表单值作为查询条件
    }
    return listType(params).then(res => ({
      total: res.total,
      list: res.rows,
    }))
  }
  catch (error) {
    console.error('获取字典类型列表失败:', error)
    return {
      total: 0,
      list: [],
    }
  }
}

/** 删除按钮操作 */
async function handleDelete(dictIds: number | number[]) {
  const isBatch = Array.isArray(dictIds)

  window.$dialog.warning({
    title: '确认删除',
    content: isBatch
      ? `是否确认删除选中的 ${dictIds.length} 个字典？`
      : '是否确认删除该字典？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await delType(dictIds.toString())
        window.$message.success('删除成功')
        // 清空选中项
        if (isBatch) {
          checkedDictTypeIds.value = []
        }
        submit()
      }
      catch (error: any) {
        console.error('删除失败:', error)
      }
    },
  })
}

/** 刷新缓存按钮操作 */
async function handleRefreshCache() {
  try {
    useDictStore().cleanDict()
    window.$message.success('刷新成功')
  }
  catch (error) {
    console.error('刷新缓存失败:', error)
  }
}

/** 打开字典数据抽屉 */
function openDataDrawer(row: Entity.DictType) {
  currentDictType.value = row
  drawerVisible.value = true
}

// 使用提取的字典类型表格列配置
const typeColumns = createTypeColumns({
  onEdit: row => typeFormRef.value?.openModal('edit', row),
  onDelete: handleDelete,
  onViewData: openDataDrawer,
  onStatusChange: async (row, status) => {
    try {
      await updateType({
        ...row,
        status,
      })
      window.$message.success('状态更新成功')
      submit()
    }
    catch (error) {
      console.error('状态更新失败:', error)
    }
  },
})
</script>

<template>
  <div>
    <pro-search-form
      v-bind="proSearchFormProps"
      :form="searchForm"
      :columns="typeSearchColumns"
      :collapse-button-props="false"
    />

    <!-- 数据表格 -->
    <pro-data-table
      v-bind="tableProps"
      v-model:checked-row-keys="checkedDictTypeIds"
      :columns="typeColumns"
      row-key="dictId"
      :loading="searchLoading"
    >
      <template #title>
        <n-button type="primary" @click="typeFormRef.openModal('add')">
          <template #icon>
            <icon-park-outline-plus />
          </template>
          新增
        </n-button>
      </template>
      <template #toolbar>
        <n-flex>
          <n-button type="warning" @click="handleRefreshCache">
            <template #icon>
              <icon-park-outline-refresh />
            </template>
            刷新缓存
          </n-button>
          <n-button
            type="error"
            :disabled="checkedDictTypeIds.length === 0"
            @click="handleDelete(checkedDictTypeIds)"
          >
            <template #icon>
              <icon-park-outline-delete />
            </template>
            删除
          </n-button>
        </n-flex>
      </template>
    </pro-data-table>

    <!-- 字典数据右侧抽屉 -->
    <DictDataDrawer
      v-model:visible="drawerVisible"
      :dict-type="currentDictType"
    />

    <!-- 添加或修改字典类型对话框 -->
    <TypeForm
      ref="typeFormRef"
      modal-name="字典类型"
      @success="submit"
    />
  </div>
</template>

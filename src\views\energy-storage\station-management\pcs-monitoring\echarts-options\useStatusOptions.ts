import type { ECOption } from '@/hooks'
import { useThemeVars } from 'naive-ui'

export function useStatusOptions() {
  const themeVars = useThemeVars()

  return computed(() => {
    return {
      title: {
        text: '正常',
        left: 'center',
        top: 'center',
        textStyle: {
          fontWeight: 'normal',
          color: themeVars.value.successColor,
          fontSize: 16,
        },
      },
      angleAxis: {
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        min: 0,
        max: 160,
        boundaryGap: ['0', '100'],
        startAngle: 225,
      },
      radiusAxis: {
        type: 'category',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: ['a', 'b', 'c'],
        z: 10,
      },
      polar: {
        radius: '100%',
      },
      series: [
        {
          type: 'bar',
          data: [undefined, undefined, 40],
          coordinateSystem: 'polar',
          barMaxWidth: 5,
          z: 2,
          name: '抢修项目',
          roundCap: true,
          color: '#1779ee',
        },
        {
          type: 'bar',
          data: [undefined, undefined, 110],
          z: 1,
          coordinateSystem: 'polar',
          barMaxWidth: 5,
          name: '警告事件',
          roundCap: true,
          color: '#29e2b0',
        },
        {
          type: 'bar',
          data: [undefined, undefined, 120],
          z: 0,
          silent: true,
          coordinateSystem: 'polar',
          barMaxWidth: 5,
          name: 'C',
          roundCap: true,
          color: '#F1F3F8',
          barGap: '-100%',
        },
        {
          type: 'pie',
          z: -1,
          radius: [0, '60%'],
          itemStyle: {
            borderWidth: 0,
            shadowBlur: 44,
            shadowColor: 'rgba(14,73,206,0.23)',
          },
          label: {
            show: false,
          },
          data: [{
            value: 100,
            itemStyle: {
              color: '#fff',
            },
          }],
        },
        {
          type: 'pie',
          radius: ['98%', '100%'],
          backgroundStyle: {
            color: '#5aadff',
          },
          emphasis: {
            disabled: true,
          },
          data: [
            1,
          ],
        },
        {
          type: 'gauge',
          radius: '100%',
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: themeVars.value.textColor2,
            fontSize: 8,
          },
          pointer: {
            show: false,
          },
        },
      ],
      tooltip: {
        show: false,
      },
    } as ECOption
  })
}

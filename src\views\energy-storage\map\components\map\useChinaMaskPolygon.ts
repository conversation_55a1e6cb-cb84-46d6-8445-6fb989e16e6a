import ChinaDistrictList from '@/assets/geo/china.districtList.json'
import { useThemeVars } from 'naive-ui'

export function useChinaMaskPolygon(_AMap: typeof AMap) {
  const themeVars = useThemeVars()

  // 外多边形坐标数组和内多边形坐标数组
  const outer: AMap.LngLatLike[] | AMap.LngLatLike[][] | AMap.LngLatLike[][][] = [
    new AMap.LngLat(-360, 90, true),
    new AMap.LngLat(-360, -90, true),
    new AMap.LngLat(360, -90, true),
    new AMap.LngLat(360, 90, true),
  ]
  const holes = ChinaDistrictList[0].boundaries as [[[number, number]]]

  const pathArray = [outer, ...holes]

  // @ts-expect-error https://lbs.amap.com/demo/javascript-api/example/overlayers/cover
  const polygon = new _AMap.Polygon({
    pathL: pathArray,
    strokeColor: themeVars.value.primaryColor.substring(0, 7),
    strokeWeight: 1,
    fillColor: themeVars.value.bodyColor,
    fillOpacity: 0.8,
  })

  watchEffect(() => {
    polygon.setOptions({
      fillColor: themeVars.value.bodyColor,
      strokeColor: themeVars.value.primaryColor.substring(0, 7),
    })
  })

  polygon.setPath(pathArray)

  return polygon
}

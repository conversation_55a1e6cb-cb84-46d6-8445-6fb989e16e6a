import { request } from '@/service/http'

// 获取站点列表
export function getSiteList(params: {
  current: number
  pageSize: number
  sorter?: any
  filters?: any
  site_number?: string
  site_name?: string
  cabinet_type?: string
  measurement_way?: string
  site_location_name?: string
}) {
  return request.Get<Api.ResponseWithRows<Entity.Site>>('/base/site/list', { params })
}

// 获取站点详情
export function getSiteDetail(id: number) {
  return request.Get<Api.ResponseWithData<Entity.Site & { siteAssetList: Array<Entity.Asset> }>>(`/base/site/${id}`)
}

// 新增站点
export function createSite(data: Entity.Site) {
  return request.Post('/base/site', data)
}

// 更新站点
export function updateSite(data: Entity.Site) {
  return request.Put(`/base/site`, data)
}

// 删除站点
export function deleteSite(id: number) {
  return request.Delete(`/base/site/${id}`)
}

// 批量删除站点
export function batchDeleteSite(ids: number[]) {
  return request.Delete('/base/site/batch', { data: { ids } })
}

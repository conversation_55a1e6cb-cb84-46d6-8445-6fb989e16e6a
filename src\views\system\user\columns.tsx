import type { DataTableColumns } from 'naive-ui'
import type { ProSearchFormColumns } from 'pro-naive-ui'
import { NButton, NSpace, NSwitch, NTag } from 'naive-ui'
import { renderProCopyableText } from 'pro-naive-ui'
import type { UserQueryParams } from '@/service/api/system/user'

// 搜索表单接口
export interface SearchFormData extends UserQueryParams {
  createTime?: [string, string]
}

// 搜索表单配置
export const searchColumns: ProSearchFormColumns<SearchFormData> = [
  {
    title: '用户名',
    path: 'userName',
    field: 'input',
    fieldProps: {
      clearable: true,
    },
  },
  {
    title: '手机号',
    path: 'phonenumber',
    field: 'input',
    fieldProps: {
      clearable: true,
    },
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      clearable: true,
      options: [
        { label: '启用', value: '0' },
        { label: '禁用', value: '1' },
      ],
    },
  },
  {
    title: '创建时间',
    path: 'createTime',
    field: 'date-time-range',
    fieldProps: {
      clearable: true,
      format: 'yyyy-MM-dd',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      defaultTime: ['00:00:00', '23:59:59'],
    },
  },
]

// 表格列配置
export function createTableColumns(options: {
  onEdit: (row: Entity.UserInfo) => void
  onDelete: (userId: number) => void
  onStatusChange: (userId: number, status: '0' | '1') => Promise<void>
  onResetPassword: (row: Entity.UserInfo) => void
}): DataTableColumns<Entity.UserInfo> {
  const { onEdit, onDelete, onStatusChange, onResetPassword } = options

  return [
    {
      type: 'selection',
    },
    {
      title: '用户名',
      align: 'center',
      key: 'userName',
    },
    {
      title: '昵称',
      align: 'center',
      key: 'nickName',
    },
    {
      title: '性别',
      align: 'center',
      key: 'sex',
      width: 50,
      render: (row) => {
        if (row.sex) {
          return (
            <NTag type={row.sex === '0' ? 'primary' : 'success'} bordered={false}>
              {row.sex === '0' ? '男' : '女'}
            </NTag>
          )
        }
      },
    },
    {
      title: '邮箱',
      align: 'center',
      key: 'email',
    },
    {
      title: '联系方式',
      align: 'center',
      key: 'phoneNumber',
      render: (row) => {
        return row.phoneNumber && renderProCopyableText(row.phoneNumber)
      },
    },
    {
      title: '创建时间',
      align: 'center',
      key: 'createTime',
      width: 160,
      render: (row) => {
        return row.createTime
      },
    },
    {
      title: '状态',
      align: 'center',
      key: 'status',
      width: 100,
      render: (row) => {
        return (
          <NSwitch
            checkedValue="0"
            uncheckedValue="1"
            value={row.status}
            onUpdateValue={value => onStatusChange(row.userId, value)}
          >
            {{ checked: () => '启用', unchecked: () => '禁用' }}
          </NSwitch>
        )
      },
    },
    {
      title: '操作',
      align: 'center',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (row) => {
        return (
          <NSpace justify="center">
            <NButton
              text
              onClick={() => onEdit(row)}
            >
              编辑
            </NButton>
            <NButton
              text
              type="warning"
              onClick={() => onResetPassword(row)}
            >
              重置密码
            </NButton>
            <NButton
              text
              type="error"
              onClick={() => onDelete(row.userId)}
            >
              删除
            </NButton>
          </NSpace>
        )
      },
    },
  ]
}

import type { DataTableColumns } from 'naive-ui'
import { NButton, NPopconfirm, NSpace, NTag } from 'naive-ui'

export function createUserAssignColumns(options: {
  onCancelAuth: (userId: number) => void
}): DataTableColumns<Entity.UserInfo> {
  const { onCancelAuth } = options

  return [
    {
      type: 'selection',
    },
    {
      title: '用户名称',
      key: 'userName',
      width: 120,
    },
    {
      title: '用户昵称',
      key: 'nickName',
    },
    {
      title: '手机号码',
      key: 'phonenumber',
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render: (row) => {
        return (
          <NTag type={row.status === '0' ? 'success' : 'error'} bordered={false}>
            {row.status === '0' ? '正常' : '停用'}
          </NTag>
        )
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 160,
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      align: 'center',
      render: (row) => {
        return (
          <NSpace justify="center">
            <NPopconfirm onPositiveClick={() => onCancelAuth(row.userId)}>
              {{
                default: () => `确认取消${row.userName}用户的角色授权？`,
                trigger: () => (
                  <NButton text type="error">取消授权</NButton>
                ),
              }}
            </NPopconfirm>
          </NSpace>
        )
      },
    },
  ]
}

export interface CardNumber {
  id: number
  cardNumber: string
  accountOpeningTime: string
  serviceStartTime: string
  serviceStopTime: string
  periodicPackageData: string
  usedData: string
  siteId: number
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  delFlag?: string
  dataScope?: string
  params?: any
  remark?: string
}

export interface CardNumberForm {
  siteId?: number
  cardNumber?: string
}

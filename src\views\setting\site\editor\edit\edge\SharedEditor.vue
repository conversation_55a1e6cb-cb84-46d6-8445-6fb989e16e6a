<script setup lang="ts">
import type { Edge } from '@vue-flow/core'
import type { FormRules, SelectProps } from 'naive-ui'
import { CustomEdgeType, DefaultEdgeType } from '@/views/setting/site/editor/edges'

const edge = defineModel<Edge>('edge', { required: true })

const rules: FormRules = {
  type: {
    required: true,
    message: '请选择线缆类型',
  },
}

const options: SelectProps['options'] = [
  { label: '默认', value: DefaultEdgeType },
  { label: '电线', value: CustomEdgeType.Wire },
  { label: '网线', value: CustomEdgeType.Network },
]
</script>

<template>
  <n-form :model="edge" :rules="rules">
    <n-form-item path="type" label="线缆类型" required>
      <n-select v-model:value="edge.type" :options="options" placeholder="请选择线缆类型" />
    </n-form-item>
  </n-form>
</template>

<style scoped>

</style>

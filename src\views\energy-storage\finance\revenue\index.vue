<script setup lang="ts">
import BHeader from '@/components/big-screen/BHeader.vue'
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
import QueryForm from './components/QueryForm.vue'
import DailyMonthlyRevenueChart from './components/DailyMonthlyRevenueChart.vue'
import AnnualRevenueChart from './components/AnnualRevenueChart.vue'
import DischargeRevenueChart from './components/DischargeRevenueChart.vue'
import ChargingCostChart from './components/ChargingCostChart.vue'
</script>

<template>
  <EnergyStorageBackground class="h-full relative">
    <BHeader title="分布式储能运营云平台" />
    <main class="revenue-dashboard h-[calc(100%-80px)] relative z-1 p-3 pt-0">
      <!-- 查询表单 -->
      <QueryForm class="mb-3" />

      <!-- 图表区域 -->
      <div class="charts-grid">
        <DailyMonthlyRevenueChart />
        <AnnualRevenueChart />
        <DischargeRevenueChart />
        <ChargingCostChart />
      </div>
    </main>
  </EnergyStorageBackground>
</template>

<style lang="scss" scoped>
.revenue-dashboard {
  .charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 353px 377px;
    gap: 12px;
    height: calc(100% - 120px);
  }
}
</style>

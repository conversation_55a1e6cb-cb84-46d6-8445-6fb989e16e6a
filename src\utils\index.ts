export * from './storage'
export * from './array'
export * from './i18n'
export * from './icon'
export * from './normalize'

/**
 * 时间格式化函数
 * @param time 时间字符串或Date对象
 * @param pattern 格式化模式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export async function parseTime(time: string | Date | null | undefined, pattern = 'YYYY-MM-DD HH:mm:ss'): Promise<string> {
  if (!time)
    return ''

  const dayjs = (await import('dayjs')).default
  return dayjs(time).format(pattern)
}

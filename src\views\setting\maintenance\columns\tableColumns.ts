import type { ProDataTableColumns } from 'pro-naive-ui'

export const tableColumns: ProDataTableColumns<Entity.Maintenance> = [
  {
    type: 'selection',
  },
  {
    type: 'index',
  },
  {
    title: '维护记录ID',
    key: 'id',
    width: 100,
  },
  {
    title: '录入时间',
    key: 'createTime',
    width: 160,
  },
  {
    title: '录入用户',
    key: 'createBy',
    width: 120,
  },
  {
    title: '设备名称',
    key: 'deviceName',
    width: 150,
  },
  {
    title: '生产厂家',
    key: 'producer',
    width: 150,
  },
  {
    title: '设备型号',
    key: 'deviceModel',
    width: 120,
  },
  {
    title: '设备单位',
    key: 'deviceUnit',
    width: 100,
  },
  {
    title: '设备数量',
    key: 'deviceNum',
    width: 100,
  },
  {
    title: '设备总计',
    key: 'specification',
    width: 100,
  },
  {
    title: '备注',
    key: 'maintenanceInfo',
    width: 200,
  },
]

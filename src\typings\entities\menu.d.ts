/// <reference path="../global.d.ts"/>

/** 系统菜单相关实体定义 */
namespace Entity {
  /** 菜单数据库表字段 */
  interface Menu {
    /** 菜单ID */
    menuId: number
    /** 菜单名称 */
    menuName: string
    /** 父菜单ID（0表示顶级菜单） */
    parentId: number
    /** 显示顺序（数字越小越靠前） */
    orderNum: number
    /** 路由地址 */
    path: string
    /** 组件路径 */
    component?: string
    /** 是否为外链（0是 1否） */
    isFrame: '0' | '1'
    /** 是否缓存（0缓存 1不缓存） */
    isCache: '0' | '1'
    /** 菜单类型（M目录 C菜单 F按钮） */
    menuType: 'M' | 'C' | 'F'
    /** 菜单状态（0显示 1隐藏） */
    visible: '0' | '1'
    /** 菜单状态（0正常 1停用） */
    status: '0' | '1'
    /** 权限标识 */
    perms?: string
    /** 菜单图标 */
    icon?: string
    /** 子菜单 */
    children?: Menu[]
    /** 创建时间 */
    createTime?: string
    /** 更新时间 */
    updateTime?: string
    /** 备注 */
    remark?: string
    /** 扩展字段 */
    [key: string]: any
  }
}

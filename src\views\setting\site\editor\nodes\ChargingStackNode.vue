<script setup lang="ts">
import CellStack from '@/assets/imgs/setting/asset-management/cell-stack.png'
import ImageNode from '@/views/setting/site/editor/nodes/ImageNode.vue'
import NodeWrapper from '@/views/setting/site/editor/nodes/NodeWrapper.vue'
import type { NodeProps } from '@vue-flow/core'

defineProps<NodeProps>()
</script>

<template>
  <div>
    <NodeWrapper v-bind="$props">
      <ImageNode :image="CellStack" :meta="data.meta" />
    </NodeWrapper>
  </div>
</template>

<style scoped>

</style>

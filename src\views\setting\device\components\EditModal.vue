<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { ref } from 'vue'
import ModalContent from './ModalContent.vue'
import type { DeviceInfo } from '@/service/api/setting/device'
import { updateDevice } from '@/service/api/setting/device'

const emit = defineEmits<{
  close: []
}>()
const loading = ref(false)

const modalForm = createProModalForm({
  onSubmit: async (values) => {
    loading.value = true
    try {
      await updateDevice({ ...values, id: modalForm.values.value.id } as DeviceInfo)
      window.$message.success('修改设备成功')
      modalForm.close()
      emit('close')
    }
    finally {
      loading.value = false
    }
  },
})

defineExpose(modalForm)
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :loading="loading"
    title="修改设备"
    width="1062px"
  >
    <ModalContent :is-edit="true" />
  </pro-modal-form>
</template>

<style scoped>

</style>

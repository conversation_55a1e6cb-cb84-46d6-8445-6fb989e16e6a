import { request } from '../../http'

// 卡号查询参数类型
export interface CardNumberQueryParams {
  id?: number
  cardNumber?: string
  accountOpeningTime?: string
  serviceStartTime?: string
  serviceStopTime?: string
  periodicPackageData?: string
  usedData?: string
  siteId?: number
  pageNum?: number
  pageSize?: number
}

// 卡号信息类型
export interface CardNumberInfo {
  id: number
  cardNumber: string
  accountOpeningTime: string
  serviceStartTime: string
  serviceStopTime: string
  periodicPackageData: string
  usedData: string
  siteId: number
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  delFlag?: string
  dataScope?: string
  params?: any
  remark?: string
}

// 查询卡号列表
export function listCardNumber(params?: CardNumberQueryParams) {
  return request.Get<Api.ResponseWithRows<CardNumberInfo[]>>('/base/card/list', { params })
}

// 查询卡号详细
export function getCardNumber(id: number) {
  return request.Get<Api.ResponseWithData<CardNumberInfo>>(`/base/card/${id}`)
}

// 新增卡号
export function addCardNumber(data: Omit<CardNumberInfo, 'id'>) {
  return request.Post<Api.BaseResponse>('/base/card', data)
}

// 修改卡号
export function updateCardNumber(data: CardNumberInfo) {
  return request.Put<Api.BaseResponse>('/base/card', data)
}

// 删除卡号
export function delCardNumber(ids: string) {
  return request.Delete<Api.BaseResponse>(`/base/card/${ids}`)
}

// 导出卡号
export function exportCardNumber(params?: CardNumberQueryParams) {
  return request.Post('/base/card/export', { params })
}

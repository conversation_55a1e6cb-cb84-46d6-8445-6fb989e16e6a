<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { addUser, deptTreeSelect, getUser, updateUser } from '@/service/api/system/user'

const props = defineProps<{
  modalName: string
}>()

const emit = defineEmits<{
  success: []
}>()

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

const modalForm = createProModalForm<Partial<Entity.UserInfo>>({
  omitEmptyString: false,
  initialValues: {
    userName: '',
    nickName: '',
    password: '',
    email: '',
    phoneNumber: '',
    sex: '0',
    status: '0',
    deptId: undefined,
    roleIds: [],
    postIds: [],
    remark: '',
  },
  onSubmit: handleSubmit,
})

type ModalType = 'add' | 'edit'
const modalType = shallowRef<ModalType>('add')
const modalTitle = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '添加',
    edit: '编辑',
  }
  return `${titleMap[modalType.value]}${props.modalName}`
})

// 选项数据
const roleOptions = ref<Entity.Role[]>([])
const deptOptions = ref<Api.TreeNode[]>([])
const postOptions = ref<Entity.Post[]>([])

// 提交表单
async function handleSubmit(values: Partial<Entity.UserInfo>) {
  try {
    startLoading()

    if (modalType.value === 'add') {
      await addUser(values)
      window.$message.success('新增用户成功')
    }
    else {
      await updateUser({
        ...values,
        userId: modalForm.values.value.userId,
        userName: modalForm.values.value.userName,
      })
      window.$message.success('修改用户成功')
    }

    modalForm.close()
    emit('success')
  }
  catch (error) {
    console.error('提交失败:', error)
  }
  finally {
    endLoading()
  }
}

// 打开弹窗
async function openModal(type: ModalType, data?: Entity.UserInfo) {
  modalType.value = type
  modalForm.open()

  // 获取部门选项数据
  deptTreeSelect().then((res) => {
    deptOptions.value = res.data
  })

  const response = await getUser(data && data.userId)
  const { posts, roles } = response
  roleOptions.value = roles
  postOptions.value = posts

  const handlers = {
    async add() {

    },
    async edit() {
      if (!data)
        return

      modalForm.values.value = response.data
      modalForm.values.value.roleIds = response.roleIds
      modalForm.values.value.postIds = response.postIds
    },
  }
  await handlers[type]()
}

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :title="modalTitle"
    :loading="loading"
    width="800px"
  >
    <n-grid cols="2" x-gap="16">
      <n-gi v-if="modalType === 'add'">
        <pro-input
          title="用户名称"
          path="userName"
          required :field-props="{ clearable: true }"
        />
      </n-gi>

      <n-gi>
        <pro-input title="用户昵称" path="nickName" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi v-if="modalType === 'add'">
        <pro-input
          title="用户密码"
          path="password"
          required
          :field-props="{
            type: 'password',
            showPasswordOn: 'click',
            clearable: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-input title="邮箱" path="email" :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-input title="手机号码" path="phoneNumber" :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-tree-select
          title="归属部门"
          path="deptId"
          :field-props="{
            options: deptOptions,
            keyField: 'id',
            labelField: 'label',
            childrenField: 'children',
            placeholder: '请选择归属部门',
            clearable: true,
            checkStrategy: 'child',
            defaultExpandAll: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-select
          title="用户性别"
          path="sex"
          :field-props="{
            options: [
              { label: '男', value: '0' },
              { label: '女', value: '1' },
              { label: '未知', value: '2' },
            ],
            placeholder: '请选择性别',
            clearable: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-radio-group
          title="状态"
          path="status"
          :field-props="{
            options: [
              { label: '正常', value: '0' },
              { label: '停用', value: '1' },
            ],
          }"
        />
      </n-gi>

      <n-gi>
        <pro-select
          title="岗位"
          path="postIds"
          :field-props="{
            options: postOptions.map(post => ({
              label: post.postName,
              value: post.postId,
            })),
            multiple: true,
            placeholder: '请选择岗位',
            clearable: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-select
          title="角色"
          path="roleIds"
          :field-props="{
            options: roleOptions.map(role => ({
              label: role.roleName,
              value: role.roleId,
            })),
            multiple: true,
            placeholder: '请选择角色',
            clearable: true,
          }"
        />
      </n-gi>

      <n-gi :span="2">
        <pro-textarea
          title="备注"
          path="remark"
          :field-props="{
            placeholder: '请输入内容',
            rows: 2,
          }"
        />
      </n-gi>
    </n-grid>
  </pro-modal-form>
</template>

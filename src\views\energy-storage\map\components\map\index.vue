<script setup lang="ts">
import '@amap/amap-jsapi-types'
import { randomMarker } from './createMarker'
import { configureMap } from './configureMap'
import { useChinaMaskPolygon } from './useChinaMaskPolygon'

const domRef = ref<HTMLDivElement>()

const { store } = useColorMode()

const map = ref<AMap.Map | undefined>(undefined)

const AMap = await configureMap()

const polygon = useChinaMaskPolygon(AMap)

onMounted(() => {
  createMap()
})

onUnmounted(() => {
  if (map.value) {
    map.value.destroy()
  }
})

watchEffect(() => {
  if (map.value) {
    const style: Record<string, string> = {
      dark: 'amap://styles/dark',
      normal: 'amap://styles/normal',
    }
    map.value.setMapStyle(store.value === 'dark' ? style.dark : style.normal)
  }
})

async function createMap() {
  if (!domRef.value)
    return
  map.value = markRaw(new AMap.Map(domRef.value, {
    zoom: 3.8,
    center: [105.34, 36.312316],
    viewMode: '3D',
  }))

  // 设置非中国区域遮罩
  map.value.on('complete', () => {
    map.value?.add(polygon)
  })

  // 创建场站图标
  createLabel()
}

function createLabel() {
  const markers = [
    randomMarker(),
    randomMarker(),
    randomMarker(),
    randomMarker(),
    randomMarker(),
    randomMarker(),
    randomMarker(),
    randomMarker(),
  ]
  const layer = new AMap.LabelsLayer({
    zooms: [3, 20],
    zIndex: 1000,
    collision: false,
    allowCollision: false, // 可以让标注避让用户的标注,

  })

  // @ts-expect-error @amap/amap-jsapi-types 不完整
  layer.add(markers)
  // 图层添加到地图
  map.value?.add(layer)
}
</script>

<template>
  <teleport defer to=".energyStorageMap">
    <div
      ref="domRef"
      class="w-full h-full absolute left-0 top-0 z-0"
    />
  </teleport>
</template>

<style scoped>
:global(.amap-logo) {
  display: none !important;
}

:global(.amap-copyright) {
  opacity: 0;
}
</style>

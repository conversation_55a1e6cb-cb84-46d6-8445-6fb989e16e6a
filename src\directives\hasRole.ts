import type { App } from 'vue'
import { useAuthStore } from '@/store'

export function install(app: App) {
  function updateRole(el: HTMLElement, roles: string | string[]) {
    if (!roles) {
      throw new Error('v-hasRole 指令需要设置角色标识')
    }

    const authStore = useAuthStore()
    const userRoles = authStore.roles
    const superAdmin = 'admin'

    // 确保 roles 是数组
    const roleArray = Array.isArray(roles) ? roles : [roles]

    if (roleArray.length === 0) {
      throw new Error('请设置角色权限标签值')
    }

    // 检查是否有角色权限
    const hasRole = userRoles.some((role) => {
      return superAdmin === role || roleArray.includes(role)
    })

    if (!hasRole) {
      el.parentNode?.removeChild(el)
    }
  }

  app.directive('hasRole', {
    mounted(el, binding) {
      updateRole(el, binding.value)
    },
  })
}

export default {
  install,
}

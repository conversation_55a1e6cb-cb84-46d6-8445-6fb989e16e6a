<script setup lang="ts">
import { useEcharts } from '@/hooks'
import { useTemperatureMonitoringOptions } from '../echarts-options/useTemperatureMonitoringOptions'

useEcharts('echarts', useTemperatureMonitoringOptions())

const temperatureType = ref('PCS')
</script>

<template>
  <BCard title="温度监控">
    <template #extend>
      <NSpace>
        <NRadioGroup v-model:value="temperatureType" size="small">
          <NRadioButton value="PCS">
            PCS仓温度
          </NRadioButton>
          <NRadioButton value="battery">
            电池仓温度
          </NRadioButton>
        </NRadioGroup>
        <NDatePicker size="small" />
      </NSpace>
    </template>
    <div ref="echarts" class="w-full h-full" />
  </BCard>
</template>

<style scoped>

</style>

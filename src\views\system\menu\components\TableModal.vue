<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { addMenu, getMenu, treeselect, updateMenu } from '@/service'

interface Props {
  modalName?: string
}

const {
  modalName = '',
} = defineProps<Props>()

const emit = defineEmits<{
  success: [] // 添加成功事件，用于刷新父组件列表
}>()

const { bool: submitLoading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

const modalForm = createProModalForm<Partial<Entity.Menu>>({
  omitEmptyString: false,
  initialValues: {
    parentId: 0,
    orderNum: 0,
    isFrame: '1',
    isCache: '1',
    menuType: 'C',
    visible: '0',
    status: '0',
  },
  onSubmit: handleSubmit,
})

type ModalType = 'add' | 'edit'
const modalType = shallowRef<ModalType>('add')
const modalTitle = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '添加',
    edit: '编辑',
  }
  return `${titleMap[modalType.value]}${modalName}`
})

const { values } = modalForm
const dirTreeOptions = ref<Api.TreeNode[]>([])

// 获取菜单树选择数据
async function getMenuTreeOptions() {
  try {
    const { data } = await treeselect()
    dirTreeOptions.value = data || []
  }
  catch (error) {
    console.error('获取菜单树失败:', error)
    dirTreeOptions.value = []
  }
}

async function openModal(type: ModalType = 'add', data?: Entity.Menu) {
  modalType.value = type
  getMenuTreeOptions()
  modalForm.open()

  const handlers = {
    async add() {
      if (data?.menuId) {
        modalForm.values.value.parentId = data.menuId
        modalForm.values.value.path = `${data.path}/`
      }
    },
    async edit() {
      if (!data || !('menuId' in data) || !data.menuId)
        return

      // 编辑模式：获取完整的菜单信息
      try {
        const { data: menuDetail } = await getMenu(data.menuId.toString())
        modalForm.values.value = menuDetail
      }
      catch (error) {
        console.error('获取菜单详情失败:', error)
      }
    },
  }

  await handlers[type]()
}

async function handleSubmit(filedValues: Partial<Entity.Menu>) {
  try {
    startLoading()

    const handlers = {
      async add() {
        await addMenu(filedValues)
        window.$message.success('新增菜单成功')
      },
      async edit() {
        await updateMenu({
          ...filedValues,
          menuId: modalForm.values.value.menuId,
        })
        window.$message.success('更新菜单成功')
      },
    }

    await handlers[modalType.value]()
    modalForm.close()
    emit('success') // 通知父组件刷新列表
  }
  catch (error) {
    console.error('提交失败:', error)
  }
  finally {
    endLoading()
  }
}

function handlePathChange(value: string) {
  values.value.component = `${value}/index.vue`
}

defineExpose({
  openModal,
})
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :title="modalTitle"
    :loading="submitLoading"
    width="700px"
    label-width="100px"
  >
    <n-grid :cols="2">
      <n-gi>
        <pro-tree-select
          title="父级目录"
          path="parentId"
          tooltip="不填写则为顶层菜单"
          :field-props="{
            clearable: true,
            filterable: true,
            options: dirTreeOptions,
            keyField: 'id',
          }"
        />
      </n-gi>

      <n-gi>
        <pro-input
          title="菜单名称"
          path="menuName"
          required
          :tooltip="values.menuType === 'F' ? '权限类型建议使用操作名称，如：新增、删除、编辑、查看等' : ''"
          :field-props="{
            clearable: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-radio-group
          title="菜单类型"
          path="menuType"
          :field-props="{
            type: 'button',
            options: [
              { value: 'M', label: '目录' },
              { value: 'C', label: '菜单' },
              { value: 'F', label: '权限' },
            ],
          }"
        />
      </n-gi>

      <n-gi v-if="values.menuType !== 'F'">
        <pro-field
          title="图标"
          path="icon"
        >
          <template #input="{ inputProps }">
            <icon-select
              :value="inputProps.value"
              @update:value="inputProps.onUpdateValue"
            />
          </template>
        </pro-field>
      </n-gi>

      <n-gi v-if="values.menuType !== 'F'" span="2">
        <pro-input
          title="路由路径"
          path="path"
          required
          :field-props="{
            clearable: true,
            placeholder: 'Eg: system/user',
          }"
          @change="handlePathChange"
        />
      </n-gi>

      <n-gi v-if="values.menuType === 'C'" span="2">
        <pro-input
          title="组件路径"
          path="component"
          required
          :field-props="{
            clearable: true,
            placeholder: 'Eg: /system/user/index.vue',
          }"
        />
      </n-gi>

      <n-gi>
        <pro-digit
          tooltip="数字越小，同级中越靠前"
          title="排序"
          required
          path="orderNum"
          :field-props="{
            clearable: true,
          }"
        />
      </n-gi>

      <n-gi>
        <pro-input
          title="权限标识"
          path="perms"
          :required="values.menuType === 'F'"
          tooltip="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)"
          :field-props="{
            clearable: true,
            placeholder: 'Eg: system:user:add',
          }"
        />
      </n-gi>

      <n-gi v-if="values.menuType === 'C'">
        <pro-switch
          title="外链"
          path="isFrame"
          :field-props="{
            checkedValue: '0',
            uncheckedValue: '1',
          }"
        />
      </n-gi>

      <n-gi v-if="values.menuType === 'C'">
        <pro-switch
          title="页面缓存"
          path="isCache"
          :field-props="{
            checkedValue: '0',
            uncheckedValue: '1',
          }"
        />
      </n-gi>

      <n-gi>
        <pro-switch
          title="启用"
          path="status"
          :field-props="{
            checkedValue: '0',
            uncheckedValue: '1',
          }"
        >
          <template #checked>
            正常
          </template>
          <template #unchecked>
            停用
          </template>
        </pro-switch>
      </n-gi>

      <n-gi v-if="values.menuType !== 'F'">
        <pro-switch
          title="菜单显示"
          path="visible"
          tooltip="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问"
          :field-props="{
            checkedValue: '0',
            uncheckedValue: '1',
          }"
        >
          <template #checked>
            显示
          </template>
          <template #unchecked>
            隐藏
          </template>
        </pro-switch>
      </n-gi>
    </n-grid>
  </pro-modal-form>
</template>

<script setup lang="ts">
import type { FormRules } from 'naive-ui'
import type { GraphNode } from '@vue-flow/core'
import { listAll } from '@/service'
import { useRequest } from 'alova/client'
import type { NodeData } from '@/views/setting/site/editor/types/NodeData'
import { ValidationStatus } from '@/views/setting/site/editor/types/NodeData'

const node = defineModel<GraphNode<NodeData>>('node', { required: true })

const { data } = useRequest(() => listAll({ deviceType: toValue(node).data.meta.type }))

const deviceList = computed<Array<Device>>(() => data.value?.data ?? [])

interface Form {
  producer: string | null
  deviceModel: string | null
  specification: string | null
}

const rules: FormRules = {
  producer: {
    required: true,
    message: '请选择厂家',
  },
  deviceModel: {
    required: true,
    message: '请选择型号',
  },
  specification: {
    required: true,
    message: '请选择规格',
  },
}

const filter = reactive<Form>({
  producer: null,
  deviceModel: null,
  specification: null,
})

watch(() => node.value, () => {
  filter.specification = node.value.data.payload?.specification ?? null
  filter.deviceModel = node.value.data.payload?.deviceModel ?? null
  filter.producer = node.value.data.payload?.producer ?? null
}, { immediate: true })

function toOption(value: string) {
  return {
    value,
    label: value,
  }
}

function unique(data: Array<any>) {
  return Array.from(new Set(data))
}

const producerOptions = computed(() => unique(toValue(deviceList)
  .map(e => e.producer)).map(toOption))

const deviceModelOptions = computed(() => unique(toValue(deviceList)
  .filter(e => e.producer === filter.producer)
  .map(e => e.deviceModel))
  .map(toOption))

const specificationOptions = computed(() => unique(toValue(deviceList)
  .filter(e => e.producer === filter.producer && e.deviceModel === filter.deviceModel)
  .map(e => e.specification))
  .map(toOption))

const onlyDevice = computed(() => toValue(deviceList)
  .filter(e => e.producer === filter.producer
    && e.deviceModel === filter.deviceModel
    && e.specification === filter.specification),
)

function onProducerChange() {
  filter.deviceModel = null
  filter.specification = null
}

function onDeviceModelChange() {
  filter.specification = null
}

function validate() {
  if (onlyDevice.value.length === 1) {
    const [device] = toValue(onlyDevice) as [Entity.Device]
    node.value.data.payload = device
    node.value.data.validationStatus = ValidationStatus.Passed
  }
  else {
    node.value.data.payload = undefined
    node.value.data.validationStatus = ValidationStatus.Failed
  }
}

function onSpecificationChange() {
  nextTick(() => validate())
}
</script>

<template>
  <n-form :model="filter" :rules="rules">
    <n-form-item path="producer" label="生产厂家" required>
      <n-select
        v-model:value="filter.producer" :options="producerOptions" placeholder="请选择厂家" clearable
        @change="onProducerChange"
      />
    </n-form-item>
    <n-form-item path="deviceModel" label="设备型号" required>
      <n-select
        v-model:value="filter.deviceModel" :options="deviceModelOptions" placeholder="请选择型号" clearable
        @change="onDeviceModelChange"
      />
    </n-form-item>
    <n-form-item path="specification" label="单位" required>
      <n-select
        v-model:value="filter.specification"
        :options="specificationOptions"
        placeholder="请选择规格"
        clearable
        @change="onSpecificationChange"
      />
    </n-form-item>
  </n-form>
</template>

<style scoped>

</style>

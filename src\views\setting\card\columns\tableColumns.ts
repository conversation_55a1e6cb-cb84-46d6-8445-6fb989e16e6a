import type { ProDataTableColumns } from 'pro-naive-ui'
import type { CardNumber } from '@/views/setting/card/types/CardNumber'

export const tableColumns: ProDataTableColumns<CardNumber> = [
  {
    type: 'selection',
  },
  {
    type: 'index',
  },
  {
    title: '卡号',
    key: 'cardNumber',
  },
  {
    title: '开户时间',
    key: 'accountOpeningTime',
  },
  {
    title: '服务开始时间',
    key: 'serviceStartTime',
  },
  {
    title: '服务结束时间',
    key: 'serviceStopTime',
  },
  {
    title: '周期套餐流量',
    key: 'periodicPackageData',
  },
  {
    title: '已使用流量',
    key: 'usedData',
  },
  {
    title: '站点ID',
    key: 'siteId',
  },
]

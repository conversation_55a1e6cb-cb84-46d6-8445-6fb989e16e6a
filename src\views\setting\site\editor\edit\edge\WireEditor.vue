<script lang="ts" setup>
import type { FormRules, SelectProps } from 'naive-ui'
import type { EdgeProps } from '@vue-flow/core'

defineProps<EdgeProps>()

const rules: FormRules = {
  attr1: {
    required: true,
    message: '请输入属性1',
  },
  voltage: {
    required: true,
    message: '请选择电压',
  },
}

const options: SelectProps['options'] = [
  {
    label: '1000kV',
    value: '1000kV',
  },
  {
    label: '330kV',
    value: '330kV',
  },
  {
    label: '220kV',
    value: '220kV',
  },
  {
    label: '35kV',
    value: '35kV',
  },
  {
    label: '10KV',
    value: '10KV',
  },
]
</script>

<template>
  <NForm :rules="rules">
    <NFormItem path="attr1" label="电线属性1">
      <NInput
        v-model:value="data.payload.attr1"
      />
    </NFormItem>
    <NFormItem path="voltage" label="电压">
      <NSelect v-model:value="data.payload.voltage" :options="options" />
    </NFormItem>
  </NForm>
</template>

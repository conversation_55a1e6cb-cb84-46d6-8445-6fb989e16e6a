<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { ref } from 'vue'
import { updateMaintenance } from '@/service/api/setting/maintenance'

const emit = defineEmits<{
  close: []
}>()
const loading = ref(false)

const modalForm = createProModalForm({
  onSubmit: async (values) => {
    loading.value = true
    try {
      const { id } = modalForm.values.value
      await updateMaintenance({ ...values as Entity.Maintenance, id })
      window.$message.success('修改设备维护成功')
      modalForm.close()
      emit('close')
    }
    finally {
      loading.value = false
    }
  },
})

defineExpose(modalForm)
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :loading="loading"
    title="编辑设备维护"
    width="800px"
  >
    <div class="grid grid-cols-2">
      <ProInput title="设备编号" path="deviceId" required />
      <ProInput title="设备名称" path="deviceName" required />
      <ProInput title="生产厂家" path="producer" required />
      <ProInput title="设备型号" path="deviceModel" required />
      <ProInput title="设备单位" path="deviceUnit" required />
      <ProInput title="设备数量" path="deviceNum" required />
      <ProInput title="设备总计" path="specification" required />
      <ProTextarea :rows="3" title="备注" path="maintenanceInfo" class="col-span-2" required />
    </div>
  </pro-modal-form>
</template>

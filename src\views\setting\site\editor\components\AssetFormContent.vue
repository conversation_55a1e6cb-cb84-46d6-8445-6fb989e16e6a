<script setup lang="ts">
import { NGi, NGrid } from 'naive-ui'
import { ProDigit } from 'pro-naive-ui'
import { useDict } from '@/hooks'

const { options: deviceTypeOptions } = useDict('device_type')
</script>

<template>
  <NGrid :cols="2" :x-gap="24">
    <NGi>
      <ProInput
        title="设备编号"
        path="deviceId"
        placeholder="请输入设备编号"
        required
      />
    </NGi>
    <NGi>
      <ProInput
        title="设备名称"
        path="deviceName"
        placeholder="请输入设备名称"
        required
      />
    </NGi>
    <NGi>
      <ProInput
        title="生产厂家"
        path="manufacturer"
        placeholder="请输入生产厂家"
        required
      />
    </NGi>
    <NGi>
      <ProInput
        title="设备型号"
        path="deviceModel"
        placeholder="请输入设备型号"
        required
      />
    </NGi>
    <NGi>
      <ProSelect
        title="设备类型"
        path="deviceType"
        placeholder="请输入设备型号"
        :field-props="{
          options: deviceTypeOptions,
        }"
        required
      />
    </NGi>
    <NGi>
      <ProDigit
        title="设备数量"
        path="deviceNum"
        placeholder="请输入设备数量"
        :min="0"
        style="width: 100%"
        required
      />
    </NGi>
    <NGi>
      <ProInput
        title="设备单位"
        path="deviceUnit"
        placeholder="请输入设备单位"
        required
      />
    </NGi>
    <NGi>
      <ProDigit
        title="单价"
        path="price"
        placeholder="请输入设备单价"
        :min="0"
        style="width: 100%"
        required
      />
    </NGi>
    <NGi>
      <ProInput
        title="维护周期"
        path="maintenanceCycle"
        placeholder="请输入维护周期"
        required
      />
    </NGi>
    <NGi>
      <ProInput
        title="维护信息"
        path="maintenanceInfo"
        placeholder="请输入维护信息"
        required
      />
    </NGi>
    <NGi>
      <ProInput
        title="设备规格"
        path="specification"
        placeholder="请输入设备规格"
        required
      />
    </NGi>
    <NGi :span="2">
      <ProTextarea
        title="备注"
        path="remark"
        placeholder="请输入备注"
        required
      />
    </NGi>
  </NGrid>
</template>

<style scoped>

</style>

import type { ProDataTableColumns } from 'pro-naive-ui'
import { useDict } from '@/hooks'
import { renderProTags } from 'pro-naive-ui'

export function useTableColumns(): ProDataTableColumns<Entity.Site> {
  const { enumMap: status } = useDict('site_status')
  return [
    {
      type: 'selection',
    },
    {
      type: 'index',
    },
    {
      title: '站点编号',
      key: 'siteId',
    },
    {
      title: '站点名称',
      key: 'siteName',
    },
    {
      title: '状态',
      key: 'runningStatus',
      width: 100,
      align: 'center',
      render: row => row.runningStatus && renderProTags(status.value[row.runningStatus]),
    },
    {
      title: '首次上线时间',
      key: 'createTime',
    },
  ]
}

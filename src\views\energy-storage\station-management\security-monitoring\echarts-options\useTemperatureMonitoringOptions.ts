import type { ECOption } from '@/hooks'
import { useThemeVars } from 'naive-ui'

export function useTemperatureMonitoringOptions() {
  const themeVars = useThemeVars()

  return computed(() => {
    return {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        right: 50,
      },
      grid: {
        left: 20,
        right: 20,
        bottom: 5,
        top: 45,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['01:30', '02:30', '0330', '04:30', '05:30', '06:30', '07:30'],
        axisLabel: {
          color: themeVars.value.textColor1,
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: `${themeVars.value.primaryColor}55`,
          },
        },
        axisLabel: {
          formatter: '{value} °C',
          color: themeVars.value.textColor1,
        },
      },
      series: [
        {
          name: '1#储能柜',
          type: 'line',
          stack: 'Total',
          data: [120, 132, 101, 134, 90, 230, 210],
        },
        {
          name: '2#储能柜',
          type: 'line',
          stack: 'Total',
          data: [220, 182, 191, 234, 290, 330, 310],
        },
        {
          name: '3#储能柜',
          type: 'line',
          stack: 'Total',
          data: [150, 232, 201, 154, 190, 330, 410],
        },
        {
          name: '4#储能柜',
          type: 'line',
          stack: 'Total',
          data: [320, 332, 301, 334, 390, 330, 320],
        },
        {
          name: '5#储能柜',
          type: 'line',
          stack: 'Total',
          data: [820, 932, 901, 934, 1290, 1330, 1320],
        },
      ],
    } as ECOption
  })
}

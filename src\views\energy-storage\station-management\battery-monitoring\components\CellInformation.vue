<script setup lang="tsx">
import { NText } from 'naive-ui'
import type { DataTableProps } from 'naive-ui'

const data = Array.from({ length: 10 }).fill(0).map(() => Array.from<number>({ length: 10 }).fill(0))

const columns: DataTableProps['columns'] = [
  {
    title: '采集点',
    key: 'collectionPoint',
    render: () => 'xxx°C',
  },
  ...Array.from({ length: 11 }).fill(0).map((_v, index) => ({
    title: `${index + 1}号温度`,
    key: `index${index}`,
    render: () => {
      return <NText type={Math.random() > 0.9 ? 'error' : undefined}>xxx</NText>
    },
  })),
]
</script>

<template>
  <BCard title="电芯信息">
    <template #extend>
      <n-select class="w-[120px]" size="small" placeholder="温度" />
    </template>

    <n-data-table striped :max-height="250" :data="data" :columns="columns" />
  </BCard>
</template>

<style scoped>

</style>

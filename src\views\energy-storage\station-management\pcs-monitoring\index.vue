<script setup lang="ts">
import BHeader from '@/components/big-screen/BHeader.vue'
import RealTimeStatus from '@/views/energy-storage/station-management/pcs-monitoring/components/RealTimeStatus.vue'
import PowerMonitoring from '@/views/energy-storage/station-management/pcs-monitoring/components/PowerMonitoring.vue'
import DCInformation from '@/views/energy-storage/station-management/pcs-monitoring/components/DCInformation.vue'
import ACInformation from '@/views/energy-storage/station-management/pcs-monitoring/components/ACInformation.vue'
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
</script>

<template>
  <EnergyStorageBackground class="h-full">
    <BHeader title="分布式储能运营云平台" />
    <main class="grid grid-rows-2 grid-cols-[1fr_100px_1fr] h-[calc(100%-80px)] relative z-1 p-3 pt-0 gap-3">
      <RealTimeStatus />
      <PowerMonitoring class="col-span-2" />
      <ACInformation class="col-span-2" />
      <DCInformation />
    </main>
  </EnergyStorageBackground>
</template>

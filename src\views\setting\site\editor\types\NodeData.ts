import type { Toolbar } from '@/views/setting/site/editor/types/Toolbar'

export interface MetaData {
  image: string // 组件图片,图标
  name: string // 设备名称
  type: string // 自定义节点的类型
  unit: string // 单位
}

export interface Payload extends Entity.Device {

}

export enum ValidationStatus {
  Pending = 'pending',
  Passed = 'passed',
  Failed = 'failed',
}

export interface NodeData<T extends Payload = Payload> extends Toolbar {
  payload?: T
  meta: MetaData
  validationStatus: ValidationStatus
}

import type { FunctionalComponent } from 'vue'
import { NText } from 'naive-ui'
import NovaIcon from '@/components/common/NovaIcon.vue'

export const Normal: FunctionalComponent = () => (
  <NText
    type="info"
    class="flex-inline items-center gap-1 whitespace-nowrap"
  >
    <NovaIcon icon="carbon:checkmark-outline"></NovaIcon>
    正常
  </NText>
)

export const Open: FunctionalComponent = () => (
  <NText
    type="info"
    class="flex-inline items-center gap-1 whitespace-nowrap"
  >
    <NovaIcon icon="carbon:checkmark-outline"></NovaIcon>
    开启
  </NText>
)

export const Stop: FunctionalComponent = () => (
  <NText
    type="error"
    class="flex-inline items-center gap-1 whitespace-nowrap"
  >
    <NovaIcon icon="carbon:information"></NovaIcon>
    停止
  </NText>
)

export const Close: FunctionalComponent = () => (
  <NText
    type="error"
    class="flex-inline items-center gap-1 whitespace-nowrap"
  >
    <NovaIcon icon="carbon:close-outline"></NovaIcon>
    关闭
  </NText>
)

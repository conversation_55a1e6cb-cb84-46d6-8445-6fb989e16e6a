import type { Raw } from 'vue'
import BusbarEditor from '@/views/setting/site/editor/edit/node/BusbarEditor.vue'
import { createSimpleFormFactory } from '@/views/setting/site/editor/edit/simpleEditorFactory'
import { CustomNodeType } from '@/views/setting/site/editor/nodes'

export const SimpleEditor = createSimpleFormFactory({
  items: [
    { label: '样例属性', path: 'attribute1' },
  ],
  model: {},
  rules: {},
})
export const NodeEditors: Record<CustomNodeType, Raw<Component>> = {
  [CustomNodeType.Busbar]: markRaw(BusbarEditor),
  [CustomNodeType.EnergyMeter]: markRaw(SimpleEditor),
  [CustomNodeType.Bem]: markRaw(SimpleEditor),
  [CustomNodeType.EnergyStorage]: markRaw(SimpleEditor),
  [CustomNodeType.Inverter]: markRaw(SimpleEditor),
  [CustomNodeType.RelayHost]: markRaw(SimpleEditor),
  [CustomNodeType.Ems]: markRaw(SimpleEditor),
  [CustomNodeType.Switch]: markRaw(SimpleEditor),
  [CustomNodeType.ChargingPile]: markRaw(SimpleEditor),
  [CustomNodeType.ChargingStack]: markRaw(SimpleEditor),
  [CustomNodeType.GridTie]: markRaw(SimpleEditor),
  [CustomNodeType.RingMainUnit]: markRaw(SimpleEditor),
  [CustomNodeType.Lcm]: markRaw(SimpleEditor),
}

import type { GlobalThemeOverrides } from 'naive-ui'
import { createPartialInvertedVars } from 'naive-ui/es/menu/styles/light'

export const primaryCommon: GlobalThemeOverrides['common'] = {
  // 主色
  primaryColor: '#0075ff',
  primaryColorHover: '#2994ff',
  primaryColorPressed: '#005ed9',
  primaryColorSuppl: '#2994ff',

  // 错误色
  errorColor: '#f33F21',
  errorColorHover: '#ff6d4d',
  errorColorPressed: '#cc2812',
  errorColorSuppl: '#ff6d4d',
}

export const lightThemeOverrides: GlobalThemeOverrides = {
  common: {
    // 文字色
    textColor1: '#333333', // 重要文字/标题
    textColor2: '#666666', // 次要文字/icon
    textColor3: '#999999', // 辅助文字/提示

    // 边框色
    borderColor: '#d7d7d7', // 输入框/按钮边框
    dividerColor: '#e4e4e4', // 分割线/表格边框

    // 背景色
    hoverColor: '#f5f5f5', // 表格行悬停
    tableColorHover: '#f5f5f5', // 表格行悬停（复用）
    tableColorStriped: '#fafafa', // 表格隔行换色
    modalColor: '#f0f2f5', // 弹窗背景（复用tab背景）
    cardColor: '#fff', // 卡片背景（保持默认白色）
    bodyColor: '#fff', // 页面背景

    ...primaryCommon,
  },

  Menu: {
    ...createPartialInvertedVars('#686868', primaryCommon.primaryColor!, '#fff', '#AAA'),
    // 普通item
    itemIconColorHoverInverted: primaryCommon.primaryColorHover,
    itemTextColorHoverInverted: primaryCommon.primaryColorHover,

    // 子级节点item
    itemTextColorChildActiveInverted: primaryCommon.primaryColor,
    itemTextColorChildActiveHoverInverted: primaryCommon.primaryColorHover,
    itemIconColorChildActiveInverted: primaryCommon.primaryColor,
    itemIconColorChildActiveHoverInverted: primaryCommon.primaryColorHover,

    // 菜单箭头
    arrowColorHoverInverted: primaryCommon.primaryColorHover,
    arrowColorChildActiveInverted: primaryCommon.primaryColor,
    arrowColorChildActiveHoverInverted: primaryCommon.primaryColorHover,
  },
  DataTable: {
    borderColor: `${primaryCommon.primaryColor}33`,
    tdColorHover: `${primaryCommon.primaryColorHover}22`,
    thColor: 'transparent',
    tdColor: 'transparent',
    tdColorStriped: '#D1E9FF', // 隔行换色
  },
  Card: {
    color: `${primaryCommon.primaryColor}11`,
  },
}

export const darkThemeOverrides: GlobalThemeOverrides = {
  common: {
    // 文字色（亮度反序）
    textColor1: '#e5e5e5', // 重要文字/标题（最亮）
    textColor2: '#b3b3b3', // 次要文字/icon
    textColor3: '#7f7f7f', // 辅助文字/提示（最暗但仍可阅读）

    // 边框色（整体压暗）
    borderColor: '#444', // 输入框/按钮边框
    dividerColor: '#2e2e2e', // 分割线/表格边框

    // 背景色（全部换成深灰/纯黑）
    hoverColor: '#222', // 表格行悬停
    tableColorHover: '#222', // 表格行悬停（复用）
    tableColorStriped: '#1a1a1a', // 表格隔行换色
    modalColor: '#181818', // 弹窗背景（复用 tab 背景）
    cardColor: '#262626', // 卡片背景（略亮于页面背景）
    bodyColor: '#0f0f0f', // 页面背景（纯黑偏一点点灰）

    ...primaryCommon,
  },
}

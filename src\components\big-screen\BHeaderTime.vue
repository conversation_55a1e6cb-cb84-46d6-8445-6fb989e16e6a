<script setup lang="ts">
import dayjs from '@/utils/dayjs'

const date = ref(dayjs())

useIntervalFn(() => {
  date.value = dayjs()
}, 1000)
</script>

<template>
  <div class="flex items-center gap-3">
    <n-h4 class="m-0 font-bold font-mono">
      {{ date.format('HH:mm:ss') }}
    </n-h4>
    <n-el tag="div" class="w-[1px] h-[27px]" style="background-color: var(--border-color)" />
    <div class="text-sm">
      <div>
        {{ date.format('dddd') }}
      </div>
      <div>
        {{ date.format('YYYY-MM-DD') }}
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>

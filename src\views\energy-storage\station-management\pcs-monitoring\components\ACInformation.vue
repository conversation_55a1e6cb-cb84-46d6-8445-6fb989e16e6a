<script setup lang="tsx">
import type { FunctionalComponent } from 'vue'
import BatteryStatusCardFooter from '@/assets/imgs/energy-storage/station-management/battery-status-card-footer.png'
import { NText } from 'naive-ui'
import AccumulatedReleasedElectricity
  from '@/assets/imgs/energy-storage/station-management/accumulated-released-electricity.png'

const State: FunctionalComponent<{ icon: string, name: string, value: string }> = props => (
  <div
    style={{
      backgroundImage: `url(${BatteryStatusCardFooter})`,
      backgroundSize: '100% auto',
      backgroundPosition: 'left bottom',
      padding: '0 30px 25px 30px',
    }}
    class="flex bg-no-repeat items-center justify-between"
  >
    <img src={props.icon} alt="icon" />
    <NText>{props.name}</NText>
    <NText strong>{props.value}</NText>
    <NText>kWh</NText>
  </div>
)

const Item: FunctionalComponent = (props, ctx) => (
  <div
    class="border border-[#76B8F5] border-solid shadow shadow-[#C9E5FF] py-1 px-4"
  >
    {ctx.slots.default && ctx.slots.default()}
  </div>
)
</script>

<template>
  <BCard title="运行信息">
    <header class="flex gap-[100px] m-3">
      <State class="flex-1" :icon="AccumulatedReleasedElectricity" name="累计充电量" value="xxxx" />
      <State class="flex-1" :icon="AccumulatedReleasedElectricity" name="累计放电量" value="xxxx" />
    </header>
    <main class="grid grid-rows-3 grid-cols-3 gap-6 p-3">
      <Item>A相电压: XX V</Item>
      <Item>B相电压: XX V</Item>
      <Item>C相电压: XX V</Item>
      <Item>A相电压: XX V</Item>
      <Item>B相电压: XX V</Item>
      <Item>C相电压: XX V</Item>
      <Item v-for="item in 5" :key="item">
        交流有功功率: XX KW
      </Item>
    </main>
  </BCard>
</template>

<style scoped>

</style>

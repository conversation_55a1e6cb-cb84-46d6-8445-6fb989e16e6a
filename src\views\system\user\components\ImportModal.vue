<script setup lang="ts">
import { useBoolean } from '@/hooks'
import { importTemplate, importUser } from '@/service/api/system/user'
import { saveAs } from 'file-saver'

const emit = defineEmits<{
  success: []
}>()

const { bool: visible, setTrue: show, setFalse: hide } = useBoolean(false)

// 是否更新已存在的用户数据
const updateSupport = ref(false)

// 下载导入模板
async function handleDownloadTemplate() {
  try {
    const blob = await importTemplate()

    // 检查 blob 是否有效
    if (!blob || blob.size === 0) {
      throw new Error('获取到的文件为空')
    }

    saveAs(blob, `user_template_${new Date().getTime()}.xlsx`)
  }
  catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    window.$message.error(`模板下载失败: ${errorMessage}`)
  }
}

// 处理文件上传
async function handleUpload(options: any) {
  const formData = new FormData()
  formData.append('file', options.file.file)

  try {
    const { data } = await importUser(formData, updateSupport.value)
    window.$message.success(data || '导入成功')
    hide()
    emit('success')
    options.onFinish()
  }
  catch (error) {
    console.error('导入失败:', error)
    window.$message.error('导入失败')
    options.onError()
  }
}

// 暴露方法
defineExpose({
  show,
  hide,
})
</script>

<template>
  <!-- 导入弹窗 -->
  <n-modal
    v-model:show="visible"
    preset="card"
    title="用户导入"
    class="w-500px"
    :mask-closable="false"
    :segmented="{
      content: true,
      footer: true,
    }"
  >
    <n-space vertical :size="16">
      <!-- 上传区域 -->
      <n-upload
        :custom-request="handleUpload"
        :show-file-list="false"
        accept=".xlsx,.xls"
      >
        <n-upload-dragger>
          <div class="flex-col-center gap-3">
            <icon-park-outline-upload class="text-3xl" />
            将文件拖到此处，或点击上传，仅支持导入 xlsx、xls 格式文件
          </div>
        </n-upload-dragger>
      </n-upload>

      <!-- 导入选项 -->
      <n-radio-group v-model:value="updateSupport">
        <n-space vertical>
          导入选项
          <n-radio :value="false">
            仅导入新用户（跳过已存在的用户）
          </n-radio>
          <n-radio :value="true">
            更新已存在的用户数据
          </n-radio>
        </n-space>
      </n-radio-group>
    </n-space>

    <template #footer>
      <div class="flex gap-3">
        <n-button
          text
          type="primary"
          size="small"
          @click="handleDownloadTemplate"
        >
          下载模板
        </n-button>
        <n-button class="ml-auto" @click="hide">
          取消
        </n-button>
        <n-button type="primary" @click="handleDownloadTemplate">
          确定
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

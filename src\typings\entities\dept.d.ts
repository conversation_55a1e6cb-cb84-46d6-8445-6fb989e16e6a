/// <reference path="../global.d.ts"/>

/** 部门数据库表字段 */
namespace Entity {
  interface Dept {
    /** 创建者 */
    createBy: string | null
    /** 创建时间 */
    createTime: string | null
    /** 更新者 */
    updateBy: string | null
    /** 更新时间 */
    updateTime: string | null
    /** 备注 */
    remark: string | null
    /** 数据范围 */
    dataScope: string | null
    /** 部门ID */
    deptId: number
    /** 父部门ID */
    parentId: number
    /** 祖级列表 */
    ancestors: string
    /** 部门名称 */
    deptName: string
    /** 显示顺序 */
    orderNum: number
    /** 负责人 */
    leader: string | null
    /** 联系电话 */
    phone: string | null
    /** 邮箱 */
    email: string | null
    /** 部门状态（0正常 1停用） */
    status: '0' | '1'
    /** 删除标志（0代表存在 2代表删除） */
    delFlag: '0' | '2'
    /** 父部门名称 */
    parentName: string | null
    /** 子部门 */
    children: Dept[]
  }
}

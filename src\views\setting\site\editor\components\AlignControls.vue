<script setup>
import { computed } from 'vue'
import { Position, useVueFlow } from '@vue-flow/core'
import { ControlButton, Controls } from '@vue-flow/controls'
import NovaIcon from '@/components/common/NovaIcon.vue'

const { getSelectedNodes, updateNode, findNode } = useVueFlow()

// 可见性控制
const visible = computed(() => getSelectedNodes.value.length > 1)

/**
 * 水平对齐
 */
function alignHorizontal(direction) {
  const nodes = getSelectedNodes.value
  if (nodes.length < 2)
    return

  // 获取所有节点信息
  const nodeInfos = nodes.map(node => ({
    id: node.id,
    width: findNode(node.id)?.dimensions.width || 150,
    originalX: node.position.x,
    originalY: node.position.y,
  }))

  // 计算布局参数
  const totalWidth = nodeInfos.reduce((sum, n) => sum + n.width, 0)
  const minX = Math.min(...nodeInfos.map(n => n.originalX))
  const maxX = Math.max(...nodeInfos.map(n => n.originalX + n.width))
  const centerX = (minX + maxX) / 2
  const spacing = (maxX - minX - totalWidth) / (nodes.length - 1)

  // 应用对齐
  nodeInfos.forEach((node, index) => {
    let newX = node.originalX

    switch (direction) {
      case 'left':
        newX = minX
        break
      case 'right':
        newX = maxX - node.width
        break
      case 'center':
        newX = centerX - node.width / 2
        break
      case 'distribute':
        newX = minX + (index === 0
          ? 0
          : (nodeInfos.slice(0, index).reduce((sum, n) => sum + n.width, 0) + spacing * index))
        break
    }

    updateNode(node.id, {
      position: { x: newX, y: node.originalY },
      targetPosition: Position.Left,
      sourcePosition: Position.Right,
    })
  })
}

/**
 * 垂直对齐
 */
function alignVertical(direction) {
  const nodes = getSelectedNodes.value
  if (nodes.length < 2)
    return

  // 获取所有节点信息
  const nodeInfos = nodes.map(node => ({
    id: node.id,
    height: findNode(node.id)?.dimensions.height || 50,
    originalX: node.position.x,
    originalY: node.position.y,
  }))

  // 计算布局参数
  const totalHeight = nodeInfos.reduce((sum, n) => sum + n.height, 0)
  const minY = Math.min(...nodeInfos.map(n => n.originalY))
  const maxY = Math.max(...nodeInfos.map(n => n.originalY + n.height))
  const centerY = (minY + maxY) / 2
  const spacing = (maxY - minY - totalHeight) / (nodes.length - 1)

  // 应用对齐
  nodeInfos.forEach((node, index) => {
    let newY = node.originalY

    switch (direction) {
      case 'top':
        newY = minY
        break
      case 'bottom':
        newY = maxY - node.height
        break
      case 'middle':
        newY = centerY - node.height / 2
        break
      case 'distribute-vertical':
        newY = minY + (index === 0
          ? 0
          : (nodeInfos.slice(0, index).reduce((sum, n) => sum + n.height, 0) + spacing * index))
        break
    }

    updateNode(node.id, {
      position: { x: node.originalX, y: newY },
      targetPosition: Position.Top,
      sourcePosition: Position.Bottom,
    })
  })
}
</script>

<template>
  <Controls
    class="flex gap-1"
    :class="visible ? 'visible' : 'hidden'"
    position="top-center"
    :show-zoom="false"
    :show-fit-view="false"
    :show-interactive="false"
  >
    <!-- 水平对齐 -->
    <ControlButton title="左对齐" @click="alignHorizontal('left')">
      <NovaIcon class="text-[12px]!" icon="carbon:text-align-left" />
    </ControlButton>
    <ControlButton title="水平居中" @click="alignHorizontal('center')">
      <NovaIcon class="text-[12px]!" icon="carbon:text-align-center" />
    </ControlButton>
    <ControlButton title="右对齐" @click="alignHorizontal('right')">
      <NovaIcon class="text-[12px]!" icon="carbon:text-align-right" />
    </ControlButton>
    <ControlButton title="水平分散" @click="alignHorizontal('distribute')">
      <NovaIcon class="text-[12px]!" icon="carbon:text-align-justify" />
    </ControlButton>

    <!-- 垂直对齐 -->
    <ControlButton title="顶部对齐" @click="alignVertical('top')">
      <NovaIcon class="text-[12px]!" icon="carbon:align-vertical-top" />
    </ControlButton>
    <ControlButton title="垂直居中" @click="alignVertical('middle')">
      <NovaIcon class="text-[12px]!" icon="carbon:align-vertical-center" />
    </ControlButton>
    <ControlButton title="底部对齐" @click="alignVertical('bottom')">
      <NovaIcon class="text-[12px]!" icon="carbon:align-vertical-bottom" />
    </ControlButton>
    <ControlButton title="垂直分散" @click="alignVertical('distribute-vertical')">
      <NovaIcon class="text-[12px]!" icon="carbon:distribute-vertical-center" />
    </ControlButton>
  </Controls>
</template>

<style scoped>
/* 可以根据需要添加样式 */
</style>

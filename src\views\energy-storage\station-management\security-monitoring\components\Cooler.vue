<script setup lang="tsx">
import { Close, Open, Stop } from '../components/Status'
import type { DataTableColumns } from 'naive-ui'

const columns: DataTableColumns<any> = [
  {
    title: '',
    key: 'index',
    render: (_v, index) => (
      <div class="flex-inline flex-col justify-center items-center">
        <img src="/src/assets/imgs/energy-storage/station-management/cold-machine-serial-number.png" alt="icon" />
        <n-text>
          {index + 1}
          {' '}
          #
        </n-text>
      </div>
    ),
  },
  {
    title: '制冷状态',
    key: 'coolingState',
    render: () => <Stop />,
  },
  {
    title: '加热状态',
    key: 'heatingState',
    render: () => <Close />,
  },
  {
    title: '水泵状态',
    key: 'pumpStatus',
    render: () => <Open />,
  },
  {
    title: () => '风机状态',
    key: 'fanStatus',
    render: () => <Close />,
  },
  {
    title: '出水温度',
    key: 'outletWaterTemperature',
    render: () => <n-text type="info">38°C</n-text>,
  },
  {
    title: '回水温度',
    key: 'returnWaterTemperature',
    render: () => <n-text type="info">38°C</n-text>,
  },
]
</script>

<template>
  <BCard title="冷机">
    <NDataTable striped :max-height="300" :columns="columns" :data="new Array(9).fill({})" />
  </BCard>
</template>

<style scoped>

</style>

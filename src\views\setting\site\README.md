# 站点管理模块

## 功能概述

站点管理模块用于管理充电桩站点信息，包括站点的基本信息、位置信息、设备类型等。

## 字段说明

| 字段名 | 字段类型 | 说明 |
|--------|----------|------|
| site_number | varchar | 站点编号 |
| site_name | varchar | 站点名称 |
| cabinet_type | varchar | 机柜类型 |
| measurement_way | varchar | 计量方式 |
| site_pic_url | varchar | 站点照片 |
| site_location_name | varchar | 站点位置名称 |
| site_location_x | float | 站点位置X坐标 |
| site_location_y | float | 站点位置Y坐标 |
| site_card_number | varchar | 站点卡号 |

## 功能特性

- ✅ 站点列表展示
- ✅ 站点搜索筛选
- ✅ 新增站点
- ✅ 编辑站点
- ✅ 删除站点
- ✅ 批量删除
- ✅ 数据导出

## 文件结构

```
site-management/
├── index.vue                 # 主页面
├── components/               # 组件目录
│   ├── ActionButtons.vue     # 操作按钮组件
│   └── SiteModal.vue         # 站点模态框组件
├── columns/                  # 列配置目录
│   ├── useTableColumns.tsx       # 表格列配置
│   └── useSearchFormColumn.ts # 搜索表单列配置
├── types/                    # 类型定义目录
│   └── Site.ts              # 站点相关类型定义
└── README.md                # 说明文档
```

## 使用说明

1. **查看站点列表**：页面加载时自动显示站点列表
2. **搜索站点**：使用搜索表单按条件筛选站点
3. **新增站点**：点击"新增"按钮，填写站点信息后提交
4. **编辑站点**：点击操作列的"编辑"按钮，修改站点信息
5. **删除站点**：点击操作列的"删除"按钮，确认后删除
6. **批量删除**：勾选多个站点后点击"删除"按钮（按钮会显示选中数量），确认后删除
7. **导出数据**：点击"导出"按钮，导出当前筛选条件下的站点数据

## API接口

- `GET /api/site/list` - 获取站点列表
- `GET /api/site/{id}` - 获取站点详情
- `POST /api/site` - 新增站点
- `PUT /api/site/{id}` - 更新站点
- `DELETE /api/site/{id}` - 删除站点
- `DELETE /api/site/batch` - 批量删除站点
- `GET /api/site/export` - 导出站点数据

<script lang="ts" setup>
import type { EdgeProps } from '@vue-flow/core'
import { BaseEdge, getBezierPath } from '@vue-flow/core'
import { computed } from 'vue'

defineOptions({ inheritAttrs: false })

const props = defineProps<EdgeProps>()
const path = computed(() => getBezierPath(props))
</script>

<template>
  <BaseEdge
    :id="id"
    :style="{
      ...style,
      stroke: '#666',
      strokeWidth: 3,
      strokeDasharray: '5, 3',
    }"
    :path="path[0]"
    :marker-end="markerEnd"
    :label="data.text"
    :label-x="path[1]"
    :label-y="path[2]"
    :label-style="{ fill: 'white', fontSize: '10px' }"
    :label-show-bg="true"
    :label-bg-style="{ fill: '#555', opacity: 0.8 }"
    :label-bg-padding="[4, 6]"
    :label-bg-border-radius="4"
  />
</template>

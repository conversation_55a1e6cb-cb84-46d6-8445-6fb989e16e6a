import { request } from '../../http'

// 岗位查询参数类型
export interface PostQueryParams {
  postName?: string
  postCode?: string
  status?: '0' | '1'
  pageNum?: number
  pageSize?: number
}

// 查询岗位列表
export function listPost(params?: PostQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.Post[]>>('/system/post/list', { params })
}

// 查询岗位详细
export function getPost(postId: number) {
  return request.Get<Api.ResponseWithData<Entity.Post>>(`/system/post/${postId}`)
}

// 新增岗位
export function addPost(data: Partial<Entity.Post>) {
  return request.Post<Api.BaseResponse>('/system/post', data)
}

// 修改岗位
export function updatePost(data: Partial<Entity.Post>) {
  return request.Put<Api.BaseResponse>('/system/post', data)
}

// 删除岗位
export function delPost(postIds: string) {
  return request.Delete<Api.BaseResponse>(`/system/post/${postIds}`)
}

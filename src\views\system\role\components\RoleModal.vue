<script setup lang="ts">
import { useBoolean } from '@/hooks'
import { addRole, getRole, roleMenuTreeselect, updateRole } from '@/service/api/system/role'
import { treeselect } from '@/service/api/system/menu'

const props = defineProps<{
  modalName: string
}>()

const emit = defineEmits<{
  success: []
}>()

const { bool: modalVisible, setTrue: showModal, setFalse: hiddenModal } = useBoolean(false)
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

const formRef = ref()

const formDefault: Partial<Entity.Role> = {
  roleName: '',
  roleKey: '',
  roleSort: 0,
  status: '0',
  remark: '',
  menuIds: [],
  menuCheckStrictly: true,
}
const formModel = ref<Partial<Entity.Role>>({ ...formDefault })

// 菜单树相关状态
const menuTreeData = ref<Api.TreeNode[]>([])
const checkedMenuKeys = ref<number[]>([])
const expandedMenuKeys = ref<number[]>([])

type ModalType = 'add' | 'edit'
const modalType = shallowRef<ModalType>('add')
const modalTitle = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '添加',
    edit: '编辑',
  }
  return `${titleMap[modalType.value]}${props.modalName}`
})

// 获取菜单树数据
async function getMenuTreeData(roleId?: number) {
  try {
    if (roleId) {
      // 编辑时获取角色的菜单权限
      const response = await roleMenuTreeselect(roleId.toString())
      menuTreeData.value = response.menus || []
      checkedMenuKeys.value = response.checkedKeys || []
      // 默认展开所有节点
      expandedMenuKeys.value = response.menus?.map((item: Api.TreeNode) => item.id) || []
    }
    else {
      // 新增时获取所有菜单
      const response = await treeselect()
      menuTreeData.value = response.data
      checkedMenuKeys.value = []
      // 默认展开所有节点
      expandedMenuKeys.value = response.data.map((item: Api.TreeNode) => item.id)
    }
  }
  catch (error) {
    console.error('获取菜单树失败:', error)
    menuTreeData.value = []
    checkedMenuKeys.value = []
  }
}

// 展开/折叠所有菜单节点
function handleMenuExpandToggle() {
  expandedMenuKeys.value = expandedMenuKeys.value.length > 0 ? [] : menuTreeData.value.map(item => item.id)
}

// 全选/全不选菜单节点
function handleMenuSelectAll() {
  checkedMenuKeys.value = checkedMenuKeys.value.length > 0 ? [] : getAllTreeNodeIds(menuTreeData.value)
}

// 处理父子联动状态变化
function handleMenuLinkageChange(checked: boolean) {
  formModel.value.menuCheckStrictly = checked
}

// 获取所有树节点ID
function getAllTreeNodeIds(nodes: Api.TreeNode[]): number[] {
  const ids: number[] = []
  function traverse(items: Api.TreeNode[]) {
    items.forEach((item) => {
      ids.push(item.id)
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }
  traverse(nodes)
  return ids
}

async function openModal(type: ModalType = 'add', data?: Entity.Role) {
  modalType.value = type
  showModal()

  const handlers = {
    async add() {
      formModel.value = { ...formDefault }
      // 获取菜单树数据（新增时不传角色ID）
      await getMenuTreeData()
    },
    async edit() {
      if (!data)
        return
      // 获取完整的角色信息
      const response = await getRole(data.roleId)
      formModel.value = { ...response.data }
      // 获取菜单树数据（编辑时传递角色ID）
      await getMenuTreeData(data.roleId)
    },
  }
  await handlers[type]()
}

function closeModal() {
  hiddenModal()
  endLoading()
}

async function handleSubmit() {
  const handlers = {
    async add() {
      try {
        const roleData = {
          ...formModel.value,
          menuIds: checkedMenuKeys.value,
        }
        await addRole(roleData as Omit<Entity.Role, 'roleId' | 'createTime' | 'updateTime'>)
        window.$message.success('新增角色成功')
        return true
      }
      catch (error) {
        console.error('新增角色失败:', error)
        window.$message.error('新增角色失败')
        return false
      }
    },
    async edit() {
      try {
        const roleData = {
          ...formModel.value,
          menuIds: checkedMenuKeys.value,
        }
        await updateRole(roleData as Entity.Role)
        window.$message.success('修改角色成功')
        return true
      }
      catch (error) {
        console.error('修改角色失败:', error)
        window.$message.error('修改角色失败')
        return false
      }
    },
  }

  await formRef.value?.validate()

  startLoading()
  const success = await handlers[modalType.value]()
  endLoading()

  if (success) {
    emit('success')
    closeModal()
  }
}

// 表单验证规则
const rules = {
  roleName: {
    required: true,
    message: '请输入角色名称',
    trigger: 'blur',
  },
  roleKey: {
    required: true,
    message: '请输入权限字符',
    trigger: 'blur',
  },
  roleSort: {
    required: true,
    type: 'number' as const,
    message: '请输入显示顺序',
    trigger: 'blur',
  },
}

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    preset="card"
    :title="modalTitle"
    class="w-600px"
    :segmented="{
      content: true,
      footer: true,
    }"
  >
    <n-form ref="formRef" :rules="rules" label-placement="left" :model="formModel" :label-width="100">
      <n-grid :cols="2">
        <n-form-item-grid-item :span="1" label="角色名称" path="roleName">
          <n-input v-model:value="formModel.roleName" placeholder="请输入角色名称" />
        </n-form-item-grid-item>
        <n-form-item-grid-item :span="1" label="权限字符" path="roleKey">
          <n-input v-model:value="formModel.roleKey" placeholder="请输入权限字符" />
        </n-form-item-grid-item>
        <n-form-item-grid-item :span="1" label="显示顺序" path="roleSort">
          <n-input-number v-model:value="formModel.roleSort" placeholder="请输入显示顺序" class="w-full" />
        </n-form-item-grid-item>
        <n-form-item-grid-item :span="1" label="角色状态" path="status">
          <n-radio-group v-model:value="formModel.status">
            <n-space>
              <n-radio value="0">
                正常
              </n-radio>
              <n-radio value="1">
                停用
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item-grid-item>

        <n-form-item-grid-item :span="2" label="菜单权限" path="menuIds">
          <n-space vertical class="w-full">
            <n-space>
              <n-checkbox @click="handleMenuExpandToggle">
                展开/折叠
              </n-checkbox>
              <n-checkbox @click="handleMenuSelectAll">
                全选/全不选
              </n-checkbox>
              <n-checkbox
                :checked="formModel.menuCheckStrictly"
                @update:checked="handleMenuLinkageChange"
              >
                父子联动
              </n-checkbox>
            </n-space>
            <n-scrollbar style="border: 1px solid var(--n-border-color)" class="max-h-350px rd">
              <n-tree
                v-model:checked-keys="checkedMenuKeys"
                v-model:expanded-keys="expandedMenuKeys"
                :data="menuTreeData"
                key-field="id"
                label-field="label"
                children-field="children"
                checkable
                :cascade="formModel.menuCheckStrictly"
                :check-strategy="formModel.menuCheckStrictly ? 'all' : 'child'"
              />
            </n-scrollbar>
          </n-space>
        </n-form-item-grid-item>
        <n-form-item-grid-item :span="2" label="备注" path="remark">
          <n-input
            v-model:value="formModel.remark"
            type="textarea"
            placeholder="请输入备注"
            :autosize="{ minRows: 2, maxRows: 5 }"
          />
        </n-form-item-grid-item>
      </n-grid>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="closeModal">
          取消
        </n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

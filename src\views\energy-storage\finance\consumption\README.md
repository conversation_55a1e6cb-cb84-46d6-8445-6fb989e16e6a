# 能耗分析页面

## 功能概述

能耗分析页面是一个大屏可视化页面，用于展示分布式储能运营云平台的能耗数据分析和统计信息。

## 页面结构

### 主要组件

1. **查询表单 (QueryForm.vue)**
   - 电站选择下拉框
   - 开始时间和结束时间选择器
   - 查询按钮
   - 累计统计信息显示

2. **电网电量/费用图表 (GridElectricityChart.vue)**
   - 柱状图展示电网用电量和费用
   - 支持今日、本月、本年时间范围切换
   - 使用主题变量确保颜色一致性

3. **负载电量/费用图表 (LoadElectricityChart.vue)**
   - 柱状图展示负载用电量和费用
   - 支持今日、本月、本年时间范围切换
   - 与电网电量图表类似但数据不同

4. **用电量同比图表 (YearOnYearChart.vue)**
   - 面积图展示本年与去年同期的用电量对比
   - 平滑曲线和渐变填充效果

5. **电量电费分析图表 (ElectricityAnalysisChart.vue)**
   - 双饼图展示电量和电费的分布情况
   - 按尖、峰、平、谷时段分类

## 技术特点

- 使用 ECharts 实现图表可视化
- 采用项目统一的 `useEcharts` hook 管理图表
- 支持主题变量，确保暗色主题下的显示效果
- 响应式设计，支持窗口大小变化
- 使用 NaiveUI 组件库构建界面
- 遵循项目代码风格和组件规范

## 数据说明

当前使用模拟数据进行演示，包括：
- 时间序列数据（今日、本月、本年）
- 用电量和费用数据
- 同比分析数据
- 分时段统计数据

## 样式特点

- 使用 BCard 组件包装各个功能模块
- 统一的蓝色主题色调
- 半透明背景和边框效果
- 响应式网格布局

## 使用说明

1. 在查询表单中选择电站和时间范围
2. 点击查询按钮更新数据
3. 通过标签切换查看不同时间范围的数据
4. 鼠标悬停查看图表详细信息

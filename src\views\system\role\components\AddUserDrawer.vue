<script setup lang="ts">
import { useBoolean } from '@/hooks'
import { authUserSelectAll, unallocatedUserList } from '@/service/api/system/role'
import type { DataTableColumns } from 'naive-ui'

const props = defineProps<{
  roleId?: number
}>()

const emit = defineEmits<{
  success: []
}>()

// 抽屉显示状态
const visible = defineModel<boolean>('show', { default: false })

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// 用户列表数据
const userList = ref<Entity.UserInfo[]>([])
const pagination = ref({
  page: 1,
  pageSize: 10,
})
const count = ref(0)

// 搜索参数
const searchParams = ref({
  userName: '',
  phonenumber: '',
})

// 表格选择
const checkedRowKeys = ref<number[]>([])

// 获取未分配用户列表
async function getUserList() {
  if (!props.roleId)
    return

  try {
    startLoading()
    const params = {
      roleId: props.roleId,
      pageNum: pagination.value.page,
      pageSize: pagination.value.pageSize,
      ...searchParams.value,
    }

    const { rows, total } = await unallocatedUserList(params)
    userList.value = rows || []
    count.value = total || 0
  }
  catch (error) {
    console.error('获取用户列表失败:', error)
    userList.value = []
    count.value = 0
  }
  finally {
    endLoading()
  }
}

// 搜索用户
function handleSearch() {
  pagination.value.page = 1
  getUserList()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    userName: '',
    phonenumber: '',
  }
  pagination.value.page = 1
  getUserList()
}

// 分页变化
function changePage(page: number) {
  pagination.value.page = page
  getUserList()
}

// 批量授权用户
async function handleBatchAuth() {
  if (checkedRowKeys.value.length === 0) {
    window.$message.warning('请选择要授权的用户')
    return
  }

  if (!props.roleId)
    return

  try {
    await authUserSelectAll({
      roleId: props.roleId,
      userIds: checkedRowKeys.value.join(','),
    })

    window.$message.success('批量授权成功')
    closeDrawer()
    emit('success')
  }
  catch (error) {
    console.error('批量授权失败:', error)
    window.$message.error('批量授权失败')
  }
}

// 关闭抽屉
function closeDrawer() {
  visible.value = false
}

// 取消操作
function handleCancel() {
  closeDrawer()
}

// 监听 roleId 变化
watch(() => props.roleId, (newRoleId) => {
  if (newRoleId) {
    // 重置数据
    pagination.value = {
      page: 1,
      pageSize: 10,
    }
    count.value = 0
    checkedRowKeys.value = []
    searchParams.value = {
      userName: '',
      phonenumber: '',
    }
    getUserList()
  }
}, { immediate: true })

// 表格列配置
const columns: DataTableColumns<Entity.UserInfo> = [
  {
    type: 'selection',
  },
  {
    title: '用户名称',
    key: 'userName',
    width: 120,
  },
  {
    title: '用户昵称',
    key: 'nickName',
    width: 120,
  },
  {
    title: '手机号码',
    key: 'phonenumber',
    width: 120,
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row: Entity.UserInfo) => {
      return h('n-tag', {
        type: row.status === '0' ? 'success' : 'error',
      }, row.status === '0' ? '正常' : '停用')
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 160,
  },
]
</script>

<template>
  <n-drawer v-model:show="visible" :width="800" placement="right">
    <n-drawer-content title="添加用户" closable>
      <n-space vertical>
        <!-- 搜索区域 -->
        <n-form inline :model="searchParams">
          <n-form-item label="用户名称">
            <n-input
              v-model:value="searchParams.userName"
              placeholder="请输入用户名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </n-form-item>
          <n-form-item label="手机号码">
            <n-input
              v-model:value="searchParams.phonenumber"
              placeholder="请输入手机号码"
              clearable
              @keyup.enter="handleSearch"
            />
          </n-form-item>
          <n-form-item class="ml-auto">
            <n-space>
              <n-button type="primary" @click="handleSearch">
                搜索
              </n-button>
              <n-button @click="handleReset">
                重置
              </n-button>
            </n-space>
          </n-form-item>
        </n-form>

        <!-- 用户列表 -->
        <pro-data-table
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="userList"
          :loading="loading"
          row-key="userId"
        />

        <!-- 分页器 -->
        <div class="flex justify-end">
          <Pagination :count="count" @change="changePage" />
        </div>
      </n-space>

      <!-- 操作按钮 -->
      <template #footer>
        <div class="flex justify-end gap-2">
          <n-button @click="handleCancel">
            取消
          </n-button>
          <n-button
            type="primary"
            :disabled="checkedRowKeys.length === 0"
            @click="handleBatchAuth"
          >
            <template #icon>
              <icon-park-outline-people-plus-one />
            </template>
            确认授权 ({{ checkedRowKeys.length }})
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

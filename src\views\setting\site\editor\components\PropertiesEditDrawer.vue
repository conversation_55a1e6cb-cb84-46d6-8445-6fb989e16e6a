<script setup lang="ts">
import { topologyMapId } from '@/views/setting/site/editor/hooks/useControl'
import type { EdgeProps, NodeProps } from '@vue-flow/core'
import { useVueFlow } from '@vue-flow/core'
import type { NodeData } from '@/views/setting/site/editor/types/NodeData'
import NodeSharedEditor from '@/views/setting/site/editor/edit/node/SharedEditor.vue'
import type { GraphNode } from '@vue-flow/core/dist/types/node'
import type { GraphEdge } from '@vue-flow/core/dist/types/edge'
import EdgeSharedEditor from '@/views/setting/site/editor/edit/edge/SharedEditor.vue'

import { EdgeEditors } from '@/views/setting/site/editor/edit/edge'
import { NodeEditors } from '@/views/setting/site/editor/edit/node'

const {
  getSelectedNodes,
  getSelectedEdges,
  removeSelectedNodes,
  removeSelectedEdges,
} = useVueFlow(topologyMapId)

const activeNode = computed<GraphNode | undefined>(() => getSelectedNodes.value?.at(0))
const activeEdge = computed<GraphEdge | undefined>(() => getSelectedEdges.value?.at(0))

const active = computed({
  get() {
    return !!getSelectedNodes.value.length || !!getSelectedEdges.value.length
  },
  set() {
    removeSelectedNodes(getSelectedNodes.value)
    removeSelectedEdges(getSelectedEdges.value)
  },
})

const nodeData = computed<NodeData | undefined>(() => activeNode.value?.data)

const title = computed(() => `${nodeData.value?.meta.name ?? '节点'}属性设置`)

const NodeEditor = computed<Component>(() => {
  const type = activeNode.value?.type
  if (type && Object.keys(NodeEditors).includes(type)) {
    return NodeEditors[type as keyof typeof NodeEditors]
  }
  else {
    return () => null
  }
})

const EdgeEditor = computed<Component>(() => {
  const type = activeEdge.value?.type
  if (type && Object.keys(EdgeEditors).includes(type)) {
    return EdgeEditors[type as keyof typeof EdgeEditors]
  }
  else {
    return () => null
  }
})
</script>

<template>
  <n-drawer
    :show="active"
    :width="300"
    :height="200"
    placement="right"
    :trap-focus="false"
    :block-scroll="false"
    :show-mask="false"
    to=".vue-flow"
  >
    <n-drawer-content v-if="activeNode" :title="title" :native-scrollbar="false">
      <!-- 节点 共有、共享 属性 -->
      <NodeSharedEditor v-if="nodeData" v-model:node="activeNode" />
      <!-- 节点私有属性 -->
      <NodeEditor v-if="false" v-bind="activeNode as unknown as NodeProps" />
    </n-drawer-content>
    <n-drawer-content v-if="activeEdge" title="线缆属性设置" :native-scrollbar="false">
      <EdgeSharedEditor :edge="activeEdge" />
      <!-- 节点私有属性 -->
      <EdgeEditor v-bind="activeEdge as unknown as EdgeProps" />
    </n-drawer-content>
  </n-drawer>
</template>

<style scoped>

</style>

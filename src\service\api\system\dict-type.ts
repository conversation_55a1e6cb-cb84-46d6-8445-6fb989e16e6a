import { request } from '../../http'

// 字典类型查询参数
export interface DictTypeQueryParams {
  dictName?: string
  dictType?: string
  status?: '0' | '1'
  pageNum?: number
  pageSize?: number
}
// 查询字典类型列表
export function listType(params?: DictTypeQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.DictType[]>>('/system/dict/type/list', { params })
}

// 查询字典类型详细
export function getType(dictId: number) {
  return request.Get<Api.ResponseWithData<Entity.DictType>>(`/system/dict/type/${dictId}`)
}

// 新增字典类型
export function addType(data: Partial<Entity.DictType>) {
  return request.Post<Api.BaseResponse>('/system/dict/type', data)
}

// 修改字典类型
export function updateType(data: Partial<Entity.DictType>) {
  return request.Put<Api.BaseResponse>('/system/dict/type', data)
}

// 删除字典类型
export function delType(dictIds: string) {
  return request.Delete<Api.BaseResponse>(`/system/dict/type/${dictIds}`)
}

// 刷新字典缓存
export function refreshDictCache() {
  return request.Delete<Api.BaseResponse>('/system/dict/type/refreshCache')
}

// 获取字典选择框列表
export function optionselect() {
  return request.Get<Api.ResponseWithData<Entity.DictType[]>>('/system/dict/type/optionselect')
}

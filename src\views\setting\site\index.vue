<script setup lang="tsx">
import { createProSearchForm, useNDataTable } from 'pro-naive-ui'
import type { ProDataTableColumns } from 'pro-naive-ui/es/data-table/types'
import { useTableColumns } from './columns/useTableColumns'
import { useSearchFormColumn } from './columns/useSearchFormColumn'
import { router } from '@/router'
import { BatchDeleteButton, CreateButton, DeleteButton, EditButton, ExportButton } from '@/components/curd/Button'
import { batchDeleteSite, deleteSite, getSiteList } from '@/service/api/setting/site'
import type { UseNDataTableParams } from 'pro-naive-ui/es/composables/use-n-data-table'

const form = createProSearchForm<Partial<Entity.Site>>({
  initialValues: {
    siteName: '',
  },
})

const dialog = useDialog()

const tableColumns = useTableColumns()
const searchColumns = useSearchFormColumn()

// 选中的行数据
const selectedRowKeys = ref<(string | number)[]>([])

const {
  table: { tableProps },
  search: {
    proSearchFormProps,
  },
  refresh,
} = useNDataTable(fetchList, { form })

async function fetchList(params: UseNDataTableParams, formData: Record<string, any>) {
  const { current, pageSize, ...rest } = params
  const response = await getSiteList({
    pageNum: current,
    pageSize,
    ...rest,
    ...formData,
  })

  return {
    total: response.total,
    list: response.rows || [],
  }
}

// 操作列
const ActionColumn: ProDataTableColumns<Entity.Site>[number] = {
  title: '操作',
  fixed: 'right',
  width: 120,
  render(row) {
    return (
      <n-space>
        <EditButton onClick={() => onEdit(row)} />
        <DeleteButton onClick={() => onRemove(row)} />
      </n-space>
    )
  },
}

function onCreate() {
  router.push({ path: '/setting/site', query: { editor: '' } })
}

// 批量删除
function onBatchDelete() {
  const siteNames = tableProps.value.data.filter(e => toValue(selectedRowKeys).includes(e.id)).map(site => site.siteName).join('、')
  const instance = dialog.warning({
    title: '批量删除确认',
    content: `你确定删除以下站点吗？\n${siteNames}`,
    positiveText: '确定删除',
    negativeText: '取消',
    draggable: true,
    onPositiveClick: async () => {
      try {
        instance.loading = true
        await batchDeleteSite(toValue(selectedRowKeys))
        await refresh()
        window.$message.success('删除成功')
      }
      finally {
        instance.loading = false
        // 清空选择
        selectedRowKeys.value = []
      }
    },
  })
}

function onRemove(site: Entity.Site) {
  const instance = dialog.warning({
    title: '提示',
    content: `你确定删除站点"${site.siteName}"？`,
    positiveText: '确定',
    negativeText: '取消',
    draggable: true,
    onPositiveClick: async () => {
      try {
        instance.loading = true
        await deleteSite(site.id)
        await refresh()
        window.$message.success('删除成功')
      }
      finally {
        instance.loading = false
      }
    },
  })
}

function onEdit(site: Entity.Site) {
  router.push({ path: '/setting/site', query: { siteId: site.id, editor: '' } })
}
</script>

<template>
  <div>
    <pro-search-form
      :form="form"
      :columns="searchColumns"
      v-bind="proSearchFormProps"
      :collapse-button-props="false"
    />

    <pro-data-table
      v-bind="tableProps"
      v-model:checked-row-keys="selectedRowKeys"
      :columns="[...tableColumns, ActionColumn]"
      :row-key="(row) => row.id"
    >
      <template #title>
        <CreateButton @click="onCreate">
          新增
        </CreateButton>
      </template>
      <template #toolbar>
        <n-space class="gap-2">
          <BatchDeleteButton
            :disabled="selectedRowKeys.length === 0"
            @click="onBatchDelete"
          >
            {{ selectedRowKeys.length > 0 ? `删除(${selectedRowKeys.length})` : '删除' }}
          </BatchDeleteButton>
          <ExportButton />
        </n-space>
      </template>
    </pro-data-table>
  </div>
</template>

<style scoped>

</style>

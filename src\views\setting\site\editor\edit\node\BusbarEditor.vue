<script lang="ts" setup>
import type { FormRules } from 'naive-ui'
import type { NodeProps } from '@vue-flow/core'

defineProps<NodeProps>()

const rules: FormRules = {
  voltage: {
    required: true,
    message: '请选择电压',
  },
}

const VoltageOptions = [
  { label: '10KV', value: '10VK' },
]
</script>

<template>
  <NForm :rules="rules">
    <NFormItem path="voltage" label="电压">
      <NSelect
        v-model:value="data.payload.voltage"
        :options="VoltageOptions"
      />
    </NFormItem>
    <NFormItem label="长度">
      <NInput />
    </NFormItem>
  </NForm>
</template>

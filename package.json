{"name": "ems-power", "type": "module", "version": "0.9.15", "private": true, "description": "", "homepage": "https://github.com/chansee97/nova-admin", "scripts": {"dev": "vite --mode dev --port 9980", "dev:prod": "vite --mode prod", "build": "vite build --mode prod", "build:dev": "vite build --mode dev", "preview": "vite preview --port 9981", "lint": "eslint . && vue-tsc --noEmit", "lint:fix": "eslint . --fix", "lint:check": "npx @eslint/config-inspector", "sizecheck": "npx vite-bundle-visualizer"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.3", "@vue-flow/core": "^1.45.1", "@vue-flow/node-toolbar": "^1.1.1", "@vueuse/core": "^13.3.0", "alova": "^3.3.4", "colord": "^2.9.3", "dayjs": "^1.11.13", "echarts": "^5.6.0", "file-saver": "^2.0.5", "md-editor-v3": "^5.6.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "qs": "^6.14.0", "quill": "^2.0.3", "radash": "^12.1.0", "vue": "^3.5.16", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1", "vue3-lottie": "^3.3.1"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.15", "@antfu/eslint-config": "^5.0.0", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify/vue": "^5.0.0", "@types/file-saver": "^2.0.7", "@types/node": "^24.0.1", "@types/qs": "^6.14.0", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "eslint": "^9.29.0", "lint-staged": "^16.1.2", "naive-ui": "^2.41.1", "pro-naive-ui": "^3.0.3", "pro-naive-ui-resolver": "^1.0.2", "sass": "^1.86.3", "simple-git-hooks": "^2.13.0", "typescript": "^5.8.3", "unocss": "^66.2.0", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "vite": "^7.0.6", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "8.0.0", "vue-tsc": "^3.0.4"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}
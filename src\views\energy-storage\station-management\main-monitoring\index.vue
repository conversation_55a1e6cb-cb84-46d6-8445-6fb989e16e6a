<script setup lang="ts">
import BHeader from '@/components/big-screen/BHeader.vue'
import IncomeOverview from './components/IncomeOverview.vue'
import StationInformation from './components/StationInformation.vue'
import AlarmEvent from './components/AlarmEvent.vue'
import StationOperationStatus from './components/StationOperationStatus.vue'
import Data from '@/assets/lottie/energy-storage/data.json'
import { Vue3Lottie } from 'vue3-lottie'
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'

const lottieContainer = ref()

useIntervalFn(() => {
  lottieContainer.value.updateDocumentData({ t: `${(Math.random() * 100).toFixed(0)}kWh`, s: 20 }, 0)
  lottieContainer.value.updateDocumentData({ t: `${(Math.random() * 100).toFixed(0)}kWh`, s: 20 }, 1)
}, 1e3)
</script>

<template>
  <EnergyStorageBackground class="h-full relative">
    <BHeader title="分布式储能运营云平台" />
    <main class="main-monitoring h-[calc(100%-80px)] flex relative z-1 p-3 pt-0 gap-3">
      <IncomeOverview class="row-span-2" />
      <Vue3Lottie ref="lottieContainer" class="row-span-2" :animation-data="Data" />
      <StationInformation />
      <AlarmEvent />
      <StationOperationStatus class="col-span-3" />
    </main>
  </EnergyStorageBackground>
</template>

<style lang="scss" scoped>
.main-monitoring {
  display: grid;
  grid-template-rows: 120px 280px 1fr;
  grid-template-columns: 400px 1fr 400px;
}
</style>

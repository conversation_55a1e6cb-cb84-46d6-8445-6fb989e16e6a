<script setup lang="tsx">
import BatteryStatusCardFooter from '@/assets/imgs/energy-storage/station-management/battery-status-card-footer.png'
import BatteryRunningState from '@/assets/imgs/energy-storage/station-management/battery-running-state.png'
import BatteryDischargeStatus from '@/assets/imgs/energy-storage/station-management/battery-discharge-status.png'
import type { FunctionalComponent } from 'vue'
import { h } from 'vue'
import { NText } from 'naive-ui'

import NumberOfCycles from '@/assets/imgs/energy-storage/station-management/number-of-cycles.png'
import InsulationResistance from '@/assets/imgs/energy-storage/station-management/insulation-resistance.png'
import MainContactor from '@/assets/imgs/energy-storage/station-management/main-contactor.png'
import PreChargingContactor from '@/assets/imgs/energy-storage/station-management/pre-charging-contactor.png'
import EquilibriumState from '@/assets/imgs/energy-storage/station-management/equilibrium-state.png'

const State: FunctionalComponent<{ icon: string, name: string, status: FunctionalComponent }> = props => (
  <div
    style={{
      backgroundImage: `url(${BatteryStatusCardFooter})`,
      backgroundSize: '100% auto',
      backgroundPosition: 'left bottom',
      padding: '0 30px 30px 30px',
    }}
    class="flex bg-no-repeat items-center justify-between"
  >
    <img src={props.icon} alt="icon" />
    <NText>{props.name}</NText>
    {h(props.status)}
  </div>
)

const Item: FunctionalComponent<{ icon: string, name: string, status: string }> = ({ icon, name, status }) => (
  <div
    class="p-5 flex items-center justify-between border-solid border border-[#84BDF2]"
  >
    <img src={icon} alt="icon" />
    <NText class="w-[8em]">{name}</NText>
    <NText class="w-[5em]">{status}</NText>
  </div>
)
</script>

<template>
  <BCard title="电池状态">
    <header class="flex gap-5 m-3">
      <State
        class="flex-1" :icon="BatteryRunningState" name="运行状态"
        :status="() => h(NText, { type: 'success' }, '正常')"
      />
      <State
        class="flex-1" :icon="BatteryDischargeStatus" name="充放电状态"
        :status="() => h(NText, { type: 'info' }, '充电中')"
      />
    </header>

    <main class="grid grid-cols-2 gap-3">
      <Item :icon="NumberOfCycles" name="循环次数" status="XXX" />
      <Item :icon="InsulationResistance" name="绝缘抗阻" status="XXX" />
      <Item :icon="MainContactor" name="主接触器状态" status="已连接" />
      <Item :icon="PreChargingContactor" name="预充接触器" status="已预充" />
      <Item :icon="EquilibriumState" name="均衡状态" status="无均衡" />
    </main>
  </BCard>
</template>

<style scoped>

</style>

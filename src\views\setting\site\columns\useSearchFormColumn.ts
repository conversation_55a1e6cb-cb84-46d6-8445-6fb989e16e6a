import type { ProSearchFormColumns } from 'pro-naive-ui'
import { useDict } from '@/hooks'

export function useSearchFormColumn() {
  const { options: status } = useDict('site_status')
  return computed<ProSearchFormColumns<Partial<Entity.Site>>>(() => [
    {
      title: '站点编号',
      path: 'siteCardNumber',
    },
    {
      title: '站点名称',
      path: 'siteName',
    },
    {
      title: '站点状态',
      path: 'runningStatus',
      field: 'select',
      fieldProps: {
        options: status.value,
      },
    },
  ])
}

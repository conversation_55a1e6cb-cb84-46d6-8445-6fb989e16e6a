import Charging from '@/assets/imgs/energy-storage/station-management/charging-today.png'
import { CustomNodeType } from '@/views/setting/site/editor/nodes'
import LCM from '@/assets/imgs/setting/asset-management/lcm.png'
import GridConnectedCabinet from '@/assets/imgs/setting/asset-management/grid-connected-cabinet.png'
import CabinetRow from '@/assets/imgs/setting/asset-management/cabinet-row.png'
import CellStack from '@/assets/imgs/setting/asset-management/cell-stack.png'
import type { MetaData } from '@/views/setting/site/editor/types/NodeData'

const nodes: Array<MetaData> = [
  {
    image: Charging,
    name: '母线',
    type: CustomNodeType.Busbar,
    unit: '条',
  },
  {
    image: Charging,
    name: '电能表',
    type: CustomNodeType.EnergyMeter,
    unit: '个',
  },
  {
    image: Charging,
    name: 'BEM装置',
    type: CustomNodeType.Bem,
    unit: '个',
  },
  {
    image: Charging,
    name: '储能柜',
    type: CustomNodeType.EnergyStorage,
    unit: '台',
  },
  {
    image: Charging,
    name: '逆变器',
    type: CustomNodeType.Inverter,
    unit: '台',
  },
  {
    image: Charging,
    name: '中继主机',
    type: CustomNodeType.RelayHost,
    unit: '台',
  },
  {
    image: Charging,
    name: '站级EMS',
    type: CustomNodeType.Ems,
    unit: '套',
  },
  {
    image: Charging,
    name: '交换机',
    type: CustomNodeType.Switch,
    unit: '台',
  },
  {
    image: Charging,
    name: '充电桩',
    type: CustomNodeType.ChargingPile,
    unit: '台',
  },
  {
    image: CellStack,
    name: '充电堆',
    type: CustomNodeType.ChargingStack,
    unit: '台',
  },
  {
    image: CabinetRow,
    name: '并网柜',
    type: CustomNodeType.GridTie,
    unit: '台',
  },
  {
    image: GridConnectedCabinet,
    name: '环网柜',
    type: CustomNodeType.RingMainUnit,
    unit: '台',
  },
  {
    image: LCM,
    name: 'LCM模块',
    type: CustomNodeType.Lcm,
    unit: '个',
  },
]

export function useBasicNodes() {
  function getNodeImage(nodeName: string) {
    return nodes.find(node => node.name === nodeName)?.image
  }

  return {
    nodes,
    getNodeImage,
  }
}

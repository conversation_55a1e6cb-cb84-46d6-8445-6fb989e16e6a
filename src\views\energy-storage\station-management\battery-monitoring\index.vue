<script setup lang="ts">
import BHeader from '@/components/big-screen/BHeader.vue'
import BatteryInformation from './components/BatteryInformation.vue'
import BatteryStatus from './components/BatteryStatus.vue'
import CellInformation from './components/CellInformation.vue'
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
</script>

<template>
  <EnergyStorageBackground class="h-full relative">
    <BHeader title="分布式储能运营云平台" />
    <main class="grid grid-cols-2 grid-rows-[480px_350px] h-[calc(100%-80px)] relative z-1 p-3 pt-0 gap-3">
      <BatteryInformation />
      <BatteryStatus />
      <CellInformation class="col-span-2" />
    </main>
  </EnergyStorageBackground>
</template>

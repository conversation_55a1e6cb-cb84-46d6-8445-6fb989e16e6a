<script setup lang="ts">
import { useBoolean } from '@/hooks'
import { allocatedUserList, authUserCancel, authUserCancelAll } from '@/service/api/system/role'
import { createUserAssignColumns } from '../config/user-assign-columns'
import AddUserDrawer from './AddUserDrawer.vue'

const emit = defineEmits<{
  success: []
}>()

const modalVisible = ref(false)
const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// 角色信息
const roleInfo = ref<Partial<Entity.Role>>({})

// 用户列表数据
const userList = ref<Entity.UserInfo[]>([])
const pagination = ref({
  page: 1,
  pageSize: 10,
})
const count = ref(0)

// 表格选择
const checkedRowKeys = ref<number[]>([])
const showAddUserDrawer = ref(false)

// 搜索参数
const searchParams = ref({
  userName: '',
  phonenumber: '',
})

// 获取角色分配的用户列表
async function getUserList() {
  if (!roleInfo.value.roleId)
    return

  try {
    startLoading()
    const params = {
      roleId: roleInfo.value.roleId,
      pageNum: pagination.value.page,
      pageSize: pagination.value.pageSize,
      ...searchParams.value,
    }

    const { rows, total } = await allocatedUserList(params)
    userList.value = rows || []
    count.value = total || 0
  }
  catch (error) {
    console.error('获取用户列表失败:', error)
    userList.value = []
    count.value = 0
  }
  finally {
    endLoading()
  }
}

// 取消用户授权
async function handleCancelAuth(userId: number) {
  if (!roleInfo.value.roleId)
    return

  try {
    await authUserCancel({
      roleId: roleInfo.value.roleId,
      userId,
    })

    window.$message.success('取消授权成功')
    await getUserList() // 刷新列表
    emit('success')
  }
  catch (error) {
    console.error('取消授权失败:', error)
  }
}

// 搜索用户
function handleSearch() {
  pagination.value.page = 1
  getUserList()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    userName: '',
    phonenumber: '',
  }
  pagination.value.page = 1
  getUserList()
}

// 分页变化
function changePage(page: number) {
  pagination.value.page = page
  getUserList()
}

// 批量取消授权
async function handleBatchCancelAuth() {
  if (checkedRowKeys.value.length === 0) {
    window.$message.warning('请选择要取消授权的用户')
    return
  }

  if (!roleInfo.value.roleId)
    return

  try {
    await authUserCancelAll({
      roleId: roleInfo.value.roleId,
      userIds: checkedRowKeys.value.join(','),
    })

    window.$message.success('批量取消授权成功')
    checkedRowKeys.value = []
    await getUserList()
    emit('success')
  }
  catch (error) {
    console.error('批量取消授权失败:', error)
  }
}

// 打开添加用户抽屉
function handleAddUser() {
  showAddUserDrawer.value = true
}

// 添加用户成功回调
async function handleAddUserSuccess() {
  await getUserList()
  emit('success')
}

// 打开弹窗
async function openModal(role: Entity.Role) {
  modalVisible.value = true

  roleInfo.value = { ...role }

  // 重置搜索和分页
  searchParams.value = {
    userName: '',
    phonenumber: '',
  }
  pagination.value = {
    page: 1,
    pageSize: 10,
  }
  count.value = 0

  // 获取用户列表
  await getUserList()
}

// 表格列配置
const columns = createUserAssignColumns({
  onCancelAuth: handleCancelAuth,
})

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    preset="card"
    :title="`分配用户 - ${roleInfo.roleName}`"
    class="w-1000px min-h-700px"
  >
    <!-- 搜索区域 -->
    <n-form inline :model="searchParams">
      <n-form-item label="用户名称">
        <n-input
          v-model:value="searchParams.userName"
          placeholder="请输入用户名称"
          clearable
          @keyup.enter="handleSearch"
        />
      </n-form-item>
      <n-form-item label="手机号码">
        <n-input
          v-model:value="searchParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter="handleSearch"
        />
      </n-form-item>
      <n-form-item class="ml-auto">
        <n-space>
          <n-button type="primary" @click="handleSearch">
            搜索
          </n-button>
          <n-button @click="handleReset">
            重置
          </n-button>
        </n-space>
      </n-form-item>
    </n-form>

    <n-space vertical>
      <!-- 用户列表 -->
      <pro-data-table
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="userList"
        :loading="loading"
        row-key="userId"
      >
        <template #extra>
          <div class="flex justify-between">
            <n-button type="primary" @click="handleAddUser">
              <template #icon>
                <icon-park-outline-people-plus-one />
              </template>
              添加用户
            </n-button>
            <n-button
              type="error"
              :disabled="checkedRowKeys.length === 0"
              @click="handleBatchCancelAuth"
            >
              <template #icon>
                <icon-park-outline-people-minus-one />
              </template>
              批量取消授权 ({{ checkedRowKeys.length }})
            </n-button>
          </div>
        </template>
      </pro-data-table>

      <!-- 分页器 -->
      <div class="flex justify-end">
        <Pagination :count="count" @change="changePage" />
      </div>
    </n-space>
  </n-modal>

  <!-- 添加用户抽屉 -->
  <AddUserDrawer
    v-model:show="showAddUserDrawer"
    :role-id="roleInfo.roleId"
    @success="handleAddUserSuccess"
  />
</template>

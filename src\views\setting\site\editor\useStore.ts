import type { Edge, GraphNode, Node } from '@vue-flow/core'
import { useVueFlow } from '@vue-flow/core'
import { getSiteDetail } from '@/service/api/setting/site'
import { topologyMapId } from '@/views/setting/site/editor/hooks/useControl'
import type { FlowExportObject } from '@vue-flow/core/dist/types/flow'
import { AssetsType } from '@/views/setting/site/editor/types/AssetsType'

export interface SiteRuntime extends Omit<Entity.Site, 'siteTopologicalMap'> {
  siteTopologicalMap?: FlowExportObject
}

export interface State {
  step: number
  activeNode: GraphNode | null
  activeEdge: Edge | null
  assets: Array<Entity.Asset> // 手动的
  basicInfo: SiteRuntime
}

export const useStore = defineStore('station-assets-store', {
  state(): State {
    return {
      step: 1,
      activeNode: null,
      activeEdge: null,
      assets: [],
      basicInfo: {
        siteNumber: '',
        siteName: '',
        measurementWay: '',
        siteLocationName: '',
        siteLocationX: 0,
        siteLocationY: 0,
        siteCardNumber: '',
        sitePicUrl: '',
        createTime: '',
        updateTime: '',
        runningStatus: '',
        siteTopologicalMap: undefined,
      },
    }
  },
  actions: {
    async load(id: Entity.Site['id']) {
      this.$reset()

      const res = await getSiteDetail(id)

      try {
        if (res.data.siteTopologicalMap) {
          res.data.siteTopologicalMap = JSON.parse(res.data.siteTopologicalMap)
        }
        // 只保留手动添加的资产
        this.assets = res.data.siteAssetList.filter(e => e.type === AssetsType.Manual) || []
      }
      finally {
        const { fromObject } = useVueFlow(topologyMapId)

        this.basicInfo = res.data as unknown as SiteRuntime

        if (this.basicInfo.siteTopologicalMap) {
          await fromObject(this.basicInfo.siteTopologicalMap)
        }
      }
    },
  },
  getters: {
    nodes(): Array<Node> {
      return this.basicInfo.siteTopologicalMap?.nodes || []
    },
  },
})

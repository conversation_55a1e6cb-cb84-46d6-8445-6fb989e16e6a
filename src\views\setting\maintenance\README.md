# 设备维护页面

## 功能描述

设备维护页面用于管理系统中各种设备的维护记录，包括设备的基本信息、数量统计等。

## 主要功能

### 1. 查询功能
- **设备名称**: 文本输入框
- **生产厂家**: 文本输入框
- **设备型号**: 文本输入框

### 2. 数据表格字段
- 维护记录ID
- 录入时间
- 录入用户
- 设备名称
- 生产厂家
- 设备型号
- 设备单位
- 设备数量
- 设备总计
- 备注
- 操作（编辑、删除）

### 3. 操作功能
- **新增**: 打开新增模态框，填写设备维护信息
- **编辑**: 打开编辑模态框，修改现有维护记录
- **删除**: 单个删除确认
- **批量删除**: 支持多选删除
- **导出**: 导出当前查询结果

### 4. 表单字段（两列布局）
- 设备名称（必填）
- 生产厂家（必填）
- 设备型号（必填）
- 设备单位（必填）
- 设备数量（必填）
- 设备总计（必填）
- 备注（必填，单独占2列）

## API 接口

- 接口路径: `/base/maintenance`
- 支持的操作:
  - `GET /base/maintenance/list` - 查询列表
  - `GET /base/maintenance/{id}` - 查询详情
  - `POST /base/maintenance` - 新增
  - `PUT /base/maintenance` - 修改
  - `DELETE /base/maintenance/{ids}` - 删除
  - `POST /base/maintenance/export` - 导出

## 文件结构

```
maintenance/
├── index.vue                    # 主页面
├── columns/
│   ├── searchFormColumn.ts      # 搜索表单列配置
│   └── tableColumns.ts          # 表格列配置
├── components/
│   ├── CreateModal.vue          # 新增模态框
│   └── EditModal.vue            # 编辑模态框
└── README.md                    # 说明文档
```

## 使用说明

1. 页面加载时会自动获取设备维护列表
2. 可以通过搜索表单进行条件筛选（设备名称、生产厂家、设备型号）
3. 点击"新增设备维护"按钮添加新的维护记录
4. 点击操作列的"编辑"按钮修改现有记录
5. 点击操作列的"删除"按钮删除单条记录
6. 勾选多条记录后可以批量删除
7. 点击"导出"按钮导出当前查询结果

## 表单布局

新增和编辑表单采用两列布局：
- 第一行：设备名称 | 生产厂家
- 第二行：设备型号 | 设备单位
- 第三行：设备数量 | 设备总计
- 第四行：备注（占满两列）

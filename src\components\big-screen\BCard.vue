<script setup lang="ts">
import { useThemeVars } from 'naive-ui'

defineProps<{
  title: string
}>()

const themeVars = useThemeVars()
</script>

<template>
  <div class="flex flex-col rounded-md backdrop-blur-8px bg-[rgba(39,137,255,0.055)]">
    <header
      class="flex items-center justify-start gap-3 m-x-[19px] p-y-[10px]"
      :style="{ borderBottom: `1px solid ${themeVars.primaryColor}22` }"
    >
      <div class="w-[10px] h-[10px] border-3 border-solid rotate-45" :style="{ color: themeVars.primaryColor }" />
      <n-h4 class="my-0 flex-1">
        {{ title }}
      </n-h4>
      <slot name="extend" />
    </header>
    <main class="flex-1 p-2">
      <slot />
    </main>
  </div>
</template>

import type { DataTableColumns } from 'naive-ui'
import { NButton, NSpace, NTag } from 'naive-ui'
import type { ProSearchFormColumns } from 'pro-naive-ui'
import { renderProCopyableText } from 'pro-naive-ui'

// 操作日志搜索表单数据类型
export interface OperationLogSearchFormData {
  operUrl?: string
  title?: string
  operName?: string
  businessType?: number
  status?: 0 | 1
  operTime?: [string, string]
}

// 操作日志搜索表单列配置
export const searchColumns: ProSearchFormColumns<OperationLogSearchFormData> = [
  {
    title: '操作地址',
    path: 'operUrl',
    field: 'input',
    fieldProps: {
      clearable: true,
      placeholder: '请输入操作地址',
    },
  },
  {
    title: '系统模块',
    path: 'title',
    field: 'input',
    fieldProps: {
      clearable: true,
      placeholder: '请输入系统模块',
    },
  },
  {
    title: '操作人员',
    path: 'operName',
    field: 'input',
    fieldProps: {
      clearable: true,
      placeholder: '请输入操作人员',
    },
  },
  {
    title: '类型',
    path: 'businessType',
    field: 'select',
    fieldProps: {
      clearable: true,
      placeholder: '请选择操作类型',
      options: [
        { label: '其它', value: 0 },
        { label: '新增', value: 1 },
        { label: '修改', value: 2 },
        { label: '删除', value: 3 },
        { label: '授权', value: 4 },
        { label: '导出', value: 5 },
        { label: '导入', value: 6 },
        { label: '强退', value: 7 },
        { label: '生成代码', value: 8 },
        { label: '清空数据', value: 9 },
      ],
    },
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      clearable: true,
      placeholder: '请选择操作状态',
      options: [
        { label: '正常', value: '1' },
        { label: '异常', value: '0' },
      ],
    },
  },
  {
    title: '操作时间',
    path: 'operTime',
    field: 'date-time-range',
    fieldProps: {
      clearable: true,
      format: 'yyyy-MM-dd',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      defaultTime: ['00:00:00', '23:59:59'],
    },
  },
]

// 表格列配置
export function createTableColumns(options: {
  onView: (row: Entity.OperationLog) => void
  onDelete: (operId: number) => void
}): DataTableColumns<Entity.OperationLog> {
  const { onView, onDelete } = options

  return [
    {
      type: 'selection',
      width: 55,
      align: 'center',
    },
    {
      title: '日志编号',
      key: 'operId',
      width: 100,
      align: 'center',
    },
    {
      title: '系统模块',
      key: 'title',
      width: 150,
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '操作类型',
      key: 'businessType',
      width: 100,
      align: 'center',
      render: (row) => {
        const typeMap: Record<string, { text: string, type: NaiveUI.ThemeColor }> = {
          0: { text: '其它', type: 'default' },
          1: { text: '新增', type: 'success' },
          2: { text: '修改', type: 'warning' },
          3: { text: '删除', type: 'error' },
          4: { text: '授权', type: 'info' },
          5: { text: '导出', type: 'primary' },
          6: { text: '导入', type: 'primary' },
          7: { text: '强退', type: 'error' },
          8: { text: '生成代码', type: 'info' },
          9: { text: '清空数据', type: 'error' },
        }
        const config = typeMap[row.businessType || '0'] || { text: '其它', type: 'default' }
        return <NTag type={config.type} bordered={false}>{config.text}</NTag>
      },
    },
    {
      title: '操作人员',
      key: 'operName',
      width: 120,
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '主机地址',
      key: 'operIp',
      width: 130,
      align: 'center',
      render: row => renderProCopyableText(row.operIp),
    },
    {
      title: '操作地点',
      key: 'operLocation',
      width: 150,
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '操作状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: (row) => {
        return (
          <NTag type={row.status === 0 ? 'success' : 'error'} bordered={false}>
            {row.status === 0 ? '正常' : '异常'}
          </NTag>
        )
      },
    },
    {
      title: '操作日期',
      key: 'operTime',
      width: 180,
      align: 'center',
    },
    {
      title: '消耗时间',
      key: 'costTime',
      width: 100,
      align: 'center',
      render: row => `${row.costTime}ms`,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      align: 'center',
      fixed: 'right',
      render: (row) => {
        return (
          <NSpace justify="center">
            <NButton
              text
              type="primary"
              onClick={() => onView(row)}
            >
              详细
            </NButton>
            <NButton
              text
              type="error"
              onClick={() => onDelete(row.operId)}
            >
              删除
            </NButton>
          </NSpace>
        )
      },
    },
  ]
}

<script setup lang="ts">
import { computed } from 'vue'
import { useThemeVars } from 'naive-ui'
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import type { ECOption } from '@/hooks/useEcharts'

const themeVars = useThemeVars()

const electricityOptions = computed<ECOption>(() => {
  // 电量数据
  const electricityData = [
    { value: 25, name: '尖电量' },
    { value: 20, name: '峰电量' },
    { value: 15, name: '平电量' },
    { value: 18, name: '谷电量' },
    { value: 12, name: '尖峰电量' },
    { value: 10, name: '深谷电量' },
  ]

  // 定义颜色
  const colors = ['#5aacfe', '#f3cc88', '#52c41a', '#fa8c16', '#722ed1', '#eb2f96']

  return {
    color: colors,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}%',
    },
    legend: [
      {
        data: ['尖电量', '峰电量', '平电量'],
        textStyle: {
          color: themeVars.value.textColor1,
        },
        top: 10,
        left: 'center',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 20,
        orient: 'horizontal',
      },
      {
        data: ['谷电量', '尖峰电量', '深谷电量'],
        textStyle: {
          color: themeVars.value.textColor1,
        },
        top: 35,
        left: 'center',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 20,
        orient: 'horizontal',
      },
    ],
    series: [
      {
        name: '电量分布',
        type: 'pie',
        radius: ['25%', '60%'],
        center: ['50%', '65%'],
        data: electricityData,
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        label: {
          show: true,
          formatter: (params: any) => {
            return `${params.name}\n${params.value}%`
          },
          rich: {
            name: {
              color: '#666666',
              fontSize: 12,
              lineHeight: 16,
            },
            value: {
              color: '#FC6E02',
              fontSize: 12,
              lineHeight: 16,
              fontWeight: 'bold',
            },
          },
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: '#666666',
          },
        },
        itemStyle: {
          borderRadius: 2,
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        avoidLabelOverlap: true,
      },
    ],
  }
})

const costOptions = computed<ECOption>(() => {
  // 电费数据
  const costData = [
    { value: 30, name: '尖电费' },
    { value: 22, name: '峰电费' },
    { value: 16, name: '平电费' },
    { value: 15, name: '谷电费' },
    { value: 10, name: '尖峰电费' },
    { value: 7, name: '深谷电费' },
  ]

  // 定义颜色
  const colors = ['#5aacfe', '#f3cc88', '#52c41a', '#fa8c16', '#722ed1', '#eb2f96']

  return {
    color: colors,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}%',
    },
    legend: [
      {
        data: ['尖电费', '峰电费', '平电费'],
        textStyle: {
          color: themeVars.value.textColor1,
        },
        top: 10,
        left: 'center',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 20,
        orient: 'horizontal',
      },
      {
        data: ['谷电费', '尖峰电费', '深谷电费'],
        textStyle: {
          color: themeVars.value.textColor1,
        },
        top: 35,
        left: 'center',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 20,
        orient: 'horizontal',
      },
    ],
    series: [
      {
        name: '电费分布',
        type: 'pie',
        radius: '60%',
        center: ['50%', '65%'],
        data: costData,
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        label: {
          show: true,
          formatter: (params: any) => {
            return `${params.name}\n${params.value}%`
          },
          rich: {
            name: {
              color: '#666666',
              fontSize: 12,
              lineHeight: 16,
            },
            value: {
              color: '#FC6E02',
              fontSize: 12,
              lineHeight: 16,
              fontWeight: 'bold',
            },
          },
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: '#666666',
          },
        },
        itemStyle: {
          borderRadius: 2,
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        avoidLabelOverlap: true,
      },
    ],
  }
})

useEcharts('electricityChart', electricityOptions)
useEcharts('costChart', costOptions)
</script>

<template>
  <BCard title="电量电费分析">
    <div class="flex gap-4 h-full">
      <!-- 左侧电量分布图表 -->
      <div class="flex-1">
        <div ref="electricityChart" class="w-full h-full" />
      </div>

      <!-- 右侧电费分布图表 -->
      <div class="flex-1">
        <div ref="costChart" class="w-full h-full" />
      </div>
    </div>
  </BCard>
</template>

<style scoped>
:deep(.n-card__content) {
  height: calc(100% - 50px);
}
</style>

<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useBoolean } from '@/hooks'
import { addPost, getPost, updatePost } from '@/service/api/system/post'

const props = defineProps<{
  modalName: string
}>()

const emit = defineEmits<{
  success: []
}>()

const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

const modalForm = createProModalForm<Partial<Entity.Post>>({
  omitEmptyString: false,
  initialValues: {
    postCode: '',
    postName: '',
    postSort: 0,
    status: '0',
    remark: '',
  },
  onSubmit: handleSubmit,
})

type ModalType = 'add' | 'edit'
const modalType = shallowRef<ModalType>('add')
const modalTitle = computed(() => {
  const titleMap: Record<ModalType, string> = {
    add: '添加',
    edit: '编辑',
  }
  return `${titleMap[modalType.value]}${props.modalName}`
})

/** 提交表单 */
async function handleSubmit(filedValues: Partial<Entity.Post>) {
  try {
    startLoading()

    if (modalType.value === 'add') {
      await addPost(filedValues)
      window.$message.success('新增岗位成功')
    }
    else {
      await updatePost({
        ...filedValues,
        postId: modalForm.values.value.postId,
      })
      window.$message.success('修改岗位成功')
    }

    modalForm.close()
    emit('success')
  }
  catch (error) {
    console.error('提交失败:', error)
    window.$message.error('提交失败')
  }
  finally {
    endLoading()
  }
}

/** 打开弹窗 */
async function openModal(type: ModalType, data?: Entity.Post) {
  modalType.value = type
  modalForm.open()

  const handlers = {
    async add() {
    },
    async edit() {
      if (!data)
        return
      // 获取完整的岗位信息
      const response = await getPost(data.postId!)
      modalForm.values.value = response.data
    },
  }
  await handlers[type]()
}

// 暴露方法
defineExpose({
  openModal,
})
</script>

<template>
  <pro-modal-form
    :form="modalForm"
    :title="modalTitle"
    :loading="loading"
    width="500px"
  >
    <n-grid cols="1">
      <n-gi>
        <pro-input title="岗位名称" path="postName" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-input title="岗位编码" path="postCode" required :field-props="{ clearable: true }" />
      </n-gi>

      <n-gi>
        <pro-digit title="岗位顺序" path="postSort" required :field-props="{ min: 0, precision: 0 }" />
      </n-gi>

      <n-gi>
        <pro-radio-group
          title="岗位状态"
          path="status"
          :field-props="{
            options: [
              { label: '正常', value: '0' },
              { label: '停用', value: '1' },
            ],
          }"
        />
      </n-gi>

      <n-gi>
        <pro-textarea
          title="备注"
          path="remark"
          :field-props="{
            placeholder: '请输入内容',
            rows: 3,
          }"
        />
      </n-gi>
    </n-grid>
  </pro-modal-form>
</template>

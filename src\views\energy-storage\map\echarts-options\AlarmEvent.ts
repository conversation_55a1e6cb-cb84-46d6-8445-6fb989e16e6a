import type { ECOption } from '@/hooks'
import { useThemeVars } from 'naive-ui'
import * as echarts from 'echarts'

export interface Params {
  name: string
  value: number
  color: string
}

export function useEchartsOptions(params: Params) {
  const { name, value, color } = params
  const themeVars = useThemeVars()

  return computed(() => {
    // 浅色背景
    const lightColor = `${color}33`

    return {
      title: {
        text: `${value}`,
        subtext: name,
        left: 'center',
        top: 'center',
        textStyle: {
          color,
          fontSize: 18,
        },
        subtextStyle: {
          color: themeVars.value.textColor2,
          fontSize: 14,
        },
        itemGap: -4,
      },
      xAxis: { show: false },
      yAxis: { show: false },
      series: [
        // 外层数据环（动态进度）
        {
          type: 'pie',
          radius: ['70%', '80%'], // 宽度10%
          startAngle: 90,
          hoverAnimation: false,
          label: { show: false },
          data: [
            {
              value,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [ // 改为从右到左
                  { offset: 0, color: `${color}00` }, // 实际应用到终点
                  { offset: 1, color }, // 实际应用到起点
                ]),
              },
            },
            {
              value: 100 - value,
              itemStyle: { color: 'transparent' }, // 剩余部分透明
            },
          ],
        },

        // 内层背景环（始终满的浅色圆）
        {
          type: 'pie',
          radius: ['62%', '80%'], // 宽度10%
          startAngle: 90,
          hoverAnimation: false,
          label: { show: false },
          itemStyle: { color: lightColor },
          data: [100], // 完整圆环
        },
      ],
    } as ECOption
  })
}

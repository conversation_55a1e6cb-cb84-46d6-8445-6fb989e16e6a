<script setup lang="tsx">
import { columns } from '@/views/setting/site/editor/columns/columns'
import type { DataTableColumn } from 'naive-ui'
import { useStore } from '@/views/setting/site/editor/useStore'
import type { NodeData } from '@/views/setting/site/editor/types/NodeData'
import type { Node } from '@vue-flow/core'
import CreateAssets from '@/views/setting/site/editor/components/CreateAsset.vue'

import type { CreateProModalFormReturn } from 'pro-naive-ui/es/modal-form/composables/create-pro-modal-form'
import EditAsset from '@/views/setting/site/editor/components/EditAsset.vue'
import { createSite, updateSite } from '@/service/api/setting/site'
import { AssetsType } from '@/views/setting/site/editor/types/AssetsType'
import { CreateButton, DeleteButton, EditButton, ExportButton } from '@/components/curd/Button'

const store = useStore()
const createDeviceRef = ref<CreateProModalFormReturn<Entity.Asset> | null>(null)
const editDeviceRef = ref<CreateProModalFormReturn<Entity.Asset> | null>(null)

function isNodeDevice(assets: Entity.Asset) {
  return assets.type === AssetsType.Node
}

const route = useRoute()
const router = useRouter()

async function onDelete(assets: Entity.Asset) {
  if (isNodeDevice(assets)) {
    // 节点设备，不能删除
    return
  }
  window.$dialog.warning({
    title: '提示',
    content: `你确定删除资产"${assets.deviceName}"？`,
    positiveText: '确定',
    negativeText: '取消',
    draggable: true,
    onPositiveClick: async () => {
      const index = store.assets.indexOf(assets)
      store.assets.splice(index, 1)
    },
  })
}

function onEdit(device: Entity.Asset) {
  editDeviceRef.value?.setInitialValues(device)
  editDeviceRef.value?.resetFieldsValue()
  editDeviceRef.value?.open()
}

function onCreate() {
  createDeviceRef.value?.resetFieldsValue()
  createDeviceRef.value?.open()
}

const ActionColumn: DataTableColumn<Entity.Asset> = {
  title: '操作',
  key: 'action',
  render: rowData => isNodeDevice(rowData)
    ? undefined
    : (
        <n-space>
          <EditButton disabled={isNodeDevice(rowData)} onClick={() => onEdit(rowData)} />
          <DeleteButton onClick={() => onDelete(rowData)} />
        </n-space>
      ),
}

const devicesWithNode = computed<Array<Entity.Asset>>(() => store.nodes.map((node: Node<NodeData<any>>) => {
  return {
    ...node.data!.payload,
    id: undefined,
    type: AssetsType.Node,
  }
}))

async function onSave() {
  const params = {
    ...store.basicInfo,
    siteTopologicalMap: JSON.stringify(store.basicInfo.siteTopologicalMap),
    siteAssetList: [...toValue(devicesWithNode), ...store.assets],
  }
  // 判断 update or create
  if (route.query.siteId) {
    await updateSite(params)
  }
  else {
    await createSite(params)
  }

  window.$message.success('保存成功')

  await router.push({ path: '/setting/site' })
}
</script>

<template>
  <BCard title="场站资产">
    <template #extend>
      <NButton @click="store.step = 2">
        上一步
      </NButton>
      <ExportButton />
      <CreateButton @click="onCreate">
        添加设备
      </CreateButton>
      <NButton type="success" @click="onSave">
        保存
      </NButton>
    </template>
    <CreateAssets ref="createDeviceRef" />
    <EditAsset ref="editDeviceRef" />
    <NDataTable :data="[...devicesWithNode, ...store.assets]" :columns="[...columns, ActionColumn]" />
  </BCard>
</template>

<style scoped>

</style>

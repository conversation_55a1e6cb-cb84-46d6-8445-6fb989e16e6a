import type { DataTableColumns } from 'naive-ui'
import { NButton, NFlex, NSwitch } from 'naive-ui'
import type { ProSearchFormColumns } from 'pro-naive-ui'
import { optionselect } from '@/service/api/system/dict-type'

// 字典类型搜索表单数据类型
export interface DictTypeSearchFormData {
  dictName?: string
  dictType?: string
  status?: '0' | '1'
}

// 字典数据搜索表单数据类型
export interface DictDataSearchFormData {
  dictType?: string
  dictValue?: string
  status?: '0' | '1'
}

// 字典类型搜索表单列配置
export const typeSearchColumns: ProSearchFormColumns<DictTypeSearchFormData> = [
  {
    title: '字典名称',
    path: 'dictName',
    field: 'input',
  },
  {
    title: '字典类型',
    path: 'dictType',
    field: 'input',
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      clearable: true,
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
  },
]

// 创建字典数据搜索表单列配置
export async function createDataSearchColumns(): Promise<ProSearchFormColumns<DictDataSearchFormData>> {
  const { data: dictTypeOptions } = await optionselect()

  return [
    {
      title: '字典名称',
      path: 'dictType',
      field: 'select',
      fieldProps: {
        clearable: true,
        filterable: true,
        options: dictTypeOptions as any,
        labelField: 'dictName',
        valueField: 'dictType',
      },
    },
    {
      title: '字典键值',
      path: 'dictValue',
      field: 'input',
      fieldProps: {
        clearable: true,
      },
    },
    {
      title: '状态',
      path: 'status',
      field: 'select',
      fieldProps: {
        clearable: true,
        options: [
          { label: '正常', value: '0' },
          { label: '停用', value: '1' },
        ],
      },
    },
  ]
}

// 字典类型表格列配置
export function createTypeColumns(options: {
  onEdit: (row: Entity.DictType) => void
  onDelete: (rowId: number) => void
  onViewData: (row: Entity.DictType) => void
  onStatusChange: (row: Entity.DictType, status: '0' | '1') => void
}): DataTableColumns<Entity.DictType> {
  const { onEdit, onDelete, onViewData, onStatusChange } = options

  return [
    {
      type: 'selection',
    },
    {
      title: '字典名称',
      key: 'dictName',
      width: 150,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '字典类型',
      key: 'dictType',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '备注',
      key: 'remark',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 200,
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: (row) => {
        return (
          <NSwitch
            value={row.status === '0'}
            onUpdateValue={(value: boolean) => onStatusChange(row, value ? '0' : '1')}
          />
        )
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 250,
      align: 'center',
      fixed: 'right',
      render: (row) => {
        return (
          <NFlex justify="center">
            <NButton text onClick={() => onEdit(row)}>
              编辑
            </NButton>
            <NButton type="info" text onClick={() => onViewData(row)}>
              字典配置
            </NButton>
            <NButton type="error" text onClick={() => onDelete(row.dictId)}>
              删除
            </NButton>
          </NFlex>
        )
      },
    },
  ]
}

// 字典数据表格列配置
export function createDataColumns(options: {
  onEdit: (row: Entity.DictData) => void
  onDelete: (dictCode: number) => void
  onStatusChange: (row: Entity.DictData, status: '0' | '1') => void
}): DataTableColumns<Entity.DictData> {
  const { onEdit, onDelete, onStatusChange } = options

  return [
    {
      type: 'selection',
    },
    {
      title: '字典标签',
      key: 'dictLabel',
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '字典键值',
      key: 'dictValue',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '字典排序',
      key: 'dictSort',
      width: 100,
      align: 'center',
    },
    {
      title: '备注',
      key: 'remark',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 180,
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: (row) => {
        return (
          <NSwitch
            value={row.status === '0'}
            onUpdateValue={(value: boolean) => onStatusChange(row, value ? '0' : '1')}
          />
        )
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      align: 'center',
      fixed: 'right',
      render: (row) => {
        return (
          <NFlex justify="center">
            <NButton text onClick={() => onEdit(row)}>
              编辑
            </NButton>
            <NButton text type="error" onClick={() => onDelete(row.dictCode!)}>
              删除
            </NButton>
          </NFlex>
        )
      },
    },
  ]
}

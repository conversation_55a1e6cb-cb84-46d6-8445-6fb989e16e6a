import { useThemeVars } from 'naive-ui'
import type { ECOption } from '@/hooks'

const data = [{
  name: '日收入',
  value: 10,
}, {
  name: '月收入',
  value: 10,
}, {
  name: '它月收入',
  value: 10,
}]

export function useIncomeOverviewOptions() {
  const themeVars = useThemeVars()

  return computed(() => {
    return {
      color: ['#5394f9', '#52b3f9', '#03bc9b'],
      title: {
        text: '总数',
        subtext: 7789,
        textStyle: {
          color: themeVars.value.primaryColor,
          fontSize: 24,
          // align: 'center'
        },
        subtextStyle: {
          fontSize: 18,
          color: themeVars.value.textColor2,
        },
        textAlign: 'center',
        left: '39%',
        top: '40%',
      },
      grid: {
        bottom: 150,
        left: 100,
        right: '10%',
      },
      legend: {
        orient: 'vertical',
        top: 'bottom',
        right: '5%',
        textStyle: {
          color: themeVars.value.textColor2,
          fontSize: 12,
        },
        icon: 'roundRect',
        data,
      },
      series: [
        // 主要展示层的
        {
          radius: ['50%', '71%'],
          center: ['40%', '50%'],
          type: 'pie',
          label: {
            normal: {
              show: true,
              formatter: '{d}%\n{b}', // {b}表示名称，{d}表示百分比
              textStyle: {
                fontSize: 14,
                color: 'inherit', // 继承系列颜色
              },
              position: 'outside',
            },
            emphasis: {
              show: true,
            },
          },
          labelLine: {
            normal: {
              show: true,
              length: 10,
              length2: 18,
              smooth: 0.2, // 轻微弯曲
              lineStyle: {
                type: 'dashed', // 设置为虚线
                width: 1,
              },
            },
            emphasis: {
              show: true,
              lineStyle: {
                type: 'dashed',
                width: 1,
              },
            },
          },
          name: '收益概况',
          data,
        },
        // 边框的设置
        {
          radius: ['50%', '56%'],
          center: ['40%', '50%'],
          type: 'pie',
          label: {
            normal: {
              show: false,
            },
            emphasis: {
              show: false,
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
            emphasis: {
              show: false,
            },
          },
          animation: false,
          tooltip: {
            show: false,
          },
          data: [{
            value: 1,
            itemStyle: {
              color: 'rgba(250,250,250,0.3)',
            },
          }],
        },
      ],
    } as unknown as ECOption
  })
}

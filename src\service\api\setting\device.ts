import { request } from '../../http'

// 设备查询参数类型
export interface DeviceQueryParams {
  deviceId?: string
  deviceType?: string
  deviceName?: string
  deviceModel?: string
  deviceUnit?: string
  deviceNum?: string
  maintenanceCycle?: string
  maintenanceInfo?: string
  specification?: string
  producer?: string
  beginTime?: string
  endTime?: string
  createBy?: string
  updateBy?: string
  pageNum?: number
  pageSize?: number
}

// 设备信息类型
export interface DeviceInfo {
  deviceId: string
  deviceType: string
  deviceName: string
  deviceModel: string
  deviceUnit: string
  deviceNum: string
  maintenanceCycle: string
  maintenanceInfo: string
  specification: string
  producer: string
  remark: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  delFlag: string
  dataScope: string
  params: any
  // 为了兼容性，添加 id 字段（与 deviceId 相同）
  id?: string
  // 设备价格字段
  price?: number
}

// 查询设备列表
export function listDevice(params?: DeviceQueryParams) {
  return request.Get<Api.ResponseWithRows<DeviceInfo[]>>('/base/device/list', { params })
}

// 查询设备详细
export function getDevice(deviceId: string) {
  return request.Get<Api.ResponseWithData<DeviceInfo>>(`/base/device/${deviceId}`)
}

// 新增设备
export function addDevice(data: Omit<DeviceInfo, 'deviceId' | 'createTime' | 'updateTime'>) {
  return request.Post<Api.BaseResponse>('/base/device', data)
}

// 修改设备
export function updateDevice(data: DeviceInfo) {
  return request.Put<Api.BaseResponse>('/base/device', data)
}

// 删除设备
export function delDevice(deviceIds: string) {
  return request.Delete<Api.BaseResponse>(`/base/device/${deviceIds}`)
}

// 导出设备
export function exportDevice(params?: DeviceQueryParams) {
  return request.Post('/base/device/export', { params })
}

// 根据设备类型查询设备库
export function listAll(params?: Pick<DeviceInfo, 'deviceType'>) {
  return request.Get<Api.ResponseWithData<Array<Entity.Device>>>('/base/device/listAll', { params })
}

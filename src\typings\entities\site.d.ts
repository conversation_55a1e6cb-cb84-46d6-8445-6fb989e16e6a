/// <reference path="../global.d.ts"/>
namespace Entity {
  interface Site {
    id: number
    siteNumber: string // 站点编号
    siteName: string // 站点名称
    measurementWay: string // 计量方式
    sitePicUrl: string // 站点照片
    siteLocationName: string // 站点位置名称
    siteLocationX: number // 站点位置X坐标
    siteLocationY: number // 站点位置Y坐标
    siteCardNumber: string // 站点卡号
    createTime?: string
    updateTime?: string
    runningStatus?: string
    siteTopologicalMap: string
  }
}

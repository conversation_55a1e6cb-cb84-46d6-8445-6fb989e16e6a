import type { DataTableColumns } from 'naive-ui'

export const columns: DataTableColumns<Entity.Device> = [
  {
    title: '序号',
    key: 'index',
    render: (_v, index) => index + 1,
  },
  {
    title: '设备名称',
    key: 'deviceName',
  },
  {
    title: '生产厂家',
    key: 'manufacturer',
  },
  {
    title: '设备型号',
    key: 'deviceModel',
  },
  {
    title: '单位',
    key: 'deviceUnit',
  },
  {
    title: '数量',
    key: 'deviceQuantity',
  },
  {
    title: '总计',
    key: 'deviceTotal',
  },
  {
    title: '备注',
    key: 'remark',
  },
]

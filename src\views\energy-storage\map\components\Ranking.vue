<script setup lang="ts">
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import { useEchartsOptions } from '../echarts-options/Ranking'

const echartsOptions = useEchartsOptions()

useEcharts('echarts', echartsOptions)
</script>

<template>
  <BCard title="日收益排名" class="flex-[5]">
    <template #extend>
      <span>单位：万元</span>
    </template>
    <div ref="echarts" class="h-full w-full" />
  </BCard>
</template>

<style scoped>

</style>

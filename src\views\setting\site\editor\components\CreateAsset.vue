<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useStore } from '@/views/setting/site/editor/useStore'
import { AssetsType } from '@/views/setting/site/editor/types/AssetsType'
import AssetFormContent from '@/views/setting/site/editor/components/AssetFormContent.vue'

const store = useStore()

const form = createProModalForm<Partial<Entity.Asset>>({
  onSubmit(values) {
    store.assets.push({ ...toRaw(values), type: AssetsType.Manual } as Entity.Asset)
    form.close()
  },
})

defineExpose(form)
</script>

<template>
  <ProModalForm
    title="新建资产信息"
    width="800px"
    preset="card"
  >
    <AssetFormContent />
  </ProModalForm>
</template>

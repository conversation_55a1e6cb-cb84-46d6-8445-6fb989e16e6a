import { request } from '../../http'

// 操作日志查询参数
export interface OperationLogQueryParams {
  title?: string
  operName?: string
  businessType?: number
  status?: 0 | 1
  params?: {
    beginTime?: string
    endTime?: string
  }
  pageNum?: number
  pageSize?: number
}

// 查询操作日志列表
export function listOperationLog(params?: OperationLogQueryParams) {
  return request.Get<Api.ResponseWithRows<Entity.OperationLog[]>>('/monitor/operlog/list', { params })
}

// 删除操作日志
export function delOperationLog(operId: string) {
  return request.Delete<Api.BaseResponse>(`/monitor/operlog/${operId}`)
}

// 清空操作日志
export function cleanOperationLog() {
  return request.Delete<Api.BaseResponse>('/monitor/operlog/clean')
}

// 导出操作日志
export function exportOperationLog(params?: OperationLogQueryParams) {
  return request.Post<Blob>('/monitor/operlog/export', params, {
    meta: { isBlob: true },
  })
}

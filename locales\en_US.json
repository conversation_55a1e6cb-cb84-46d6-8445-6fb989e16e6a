{"common": {"cancel": "Cancel", "confirm": "Confirm", "close": "Closure", "reload": "Refresh", "choose": "<PERSON><PERSON>", "navigate": "Navigate", "inputPlaceholder": "please enter", "selectPlaceholder": "please choose"}, "app": {"loginOut": "Login out", "loginOutContent": "Confirm to log out of current account?", "loginOutTitle": "Sign out", "userCenter": "Personal center", "light": "Light", "dark": "Dark", "system": "System", "backTop": "Back to top", "toggleSider": "Toggle sidebar", "BreadcrumbIcon": "Breadcrumbs icon", "blackAndWhite": "Black and white mode", "bottomCopyright": "Bottom copyright", "breadcrumb": "Bread crumbs", "colorWeak": "Color Weakness Mode", "interfaceDisplay": "Interface display", "logoDisplay": "LOGO display", "messages": "Messages", "multitab": "Display multiple tabs", "notifications": "Notify", "notificationsTips": "Notification", "pageTransition": "Page transition", "reset": "Reset", "resetSettingContent": "Confirm to reset all settings?", "resetSettingMeaasge": "Reset successful", "resetSettingTitle": "Reset settings", "searchPlaceholder": "Search page/path", "search": "Search ( Ctrl+K )", "setting": "Setting", "systemSetting": "System settings", "themeColor": "Theme color", "themeSetting": "Theme settings", "todos": "Todos", "toggleFullScreen": "Toggle full screen", "togglContentFullScreen": "Toggle content full screen", "topProgress": "Top progress", "transitionFadeBottom": "Bottom fade", "transitionFadeScale": "Scale fade", "transitionFadeSlide": "Side fade", "transitionNull": "No transition", "transitionSoft": "Soft", "transitionZoomFade": "Expand fade out", "transitionZoomOut": "Zoom out", "watermake": "Watermark", "closeOther": "Close other", "closeAll": "Close all", "closeLeft": "Close left", "closeRight": "Close right", "backHome": "Back to the homepage", "getRouteError": "Failed to obtain route, please try again later.", "layoutSetting": "Layout settings", "verticalLayout": "Vertical layout", "horizontalLayout": "Horizontal layout", "twoColumnLayout": "Two column layout", "mixedTwoColumnLayout": "Mixed two column layout", "sidebarLayout": "Sidebar layout", "mixedSidebarLayout": "Mixed sidebar layout"}, "login": {"signInTitle": "Welcome to log in to the system", "accountRuleTip": "Please enter account", "passwordRuleTip": "Please enter password", "or": "Or", "rememberMe": "Remember me", "forgotPassword": "Forget the password?", "signIn": "Sign in", "signUp": "Sign up", "noAccountText": "Don't have an account?", "accountPlaceholder": "Enter the account number", "checkPasswordPlaceholder": "Please enter password again", "checkPasswordRuleTip": "Please confirm password again", "haveAccountText": "Do you have an account?", "passwordPlaceholder": "Enter password", "readAndAgree": "I have read and agree", "registerTitle": "Register", "userAgreement": "User Agreement", "resetPassword": "Reset password", "resetPasswordPlaceholder": "Enter account/mobile phone number", "resetPasswordRuleTip": "Please enter your account/mobile phone number", "resetPasswordTitle": "Reset"}, "route": {"appRoot": "Home", "cardList": "Card list", "draggableList": "Draggable list", "commonList": "Common list", "dashboard": "Dashboard", "demo": "Function example", "fetch": "Request example", "list": "List", "monitor": "Monitoring", "multi": "Multi-level menu", "multi2": "Multi-level menu subpage", "multi2Detail": "Details page of multi-level menu", "multi3": "multi-level menu", "multi4": "Multi-level menu 3-1", "workbench": "Workbench", "QRCode": "QR code", "about": "About", "clipboard": "Clipboard", "demo403": "403", "demo404": "404", "demo500": "500", "dictionarySetting": "Dictionary settings", "documents": "Document", "documentsVite": "Vite", "documentsVue": "<PERSON><PERSON>", "documentsVueuse": "VueUse (external link)", "documentsNova": "Nova docs", "documentsPublic": "Public page (external link)", "echarts": "Echarts", "editor": "Editor", "editorMd": "MarkDown editor", "editorRich": "Rich text editor", "error": "Exception page", "icons": "Icon", "justSuper": "Supervisible", "map": "Map", "menuSetting": "<PERSON><PERSON>", "permission": "Permissions", "permissionDemo": "Permissions example", "setting": "System settings", "userCenter": "Personal Center", "accountSetting": "User settings", "cascader": "Administrative region selection", "dict": "Dictionary example"}, "http": {"400": "Syntax error in the request", "401": "User unauthorized", "403": "Server refused access", "404": "Requested resource does not exist", "405": "Request method not allowed", "408": "Network request timed out", "500": "Internal server error", "501": "Server not implemented the requested functionality", "502": "Bad gateway", "503": "Service unavailable", "504": "Gateway timeout", "505": "HTTP version not supported for this request", "defaultTip": "Request error"}, "components": {"iconSelector": {"inputPlaceholder": "Select target icon", "searchPlaceholder": "Search icon", "clearIcon": "Clear icon", "selectorTitle": "Icon selection"}, "copyText": {"message": "<PERSON><PERSON>d successfully", "tooltip": "Copy", "unsupportedError": "Your browser does not support Clipboard API", "unpermittedError": "Crrently not permitted to use Clipboard API"}}}
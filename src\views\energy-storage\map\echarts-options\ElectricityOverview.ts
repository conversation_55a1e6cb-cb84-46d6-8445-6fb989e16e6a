import type { ECOption } from '@/hooks'
import * as echarts from 'echarts'
import { useThemeVars } from 'naive-ui'

export function useEchartsOptions() {
  const themeVars = useThemeVars() // 获取主题变量

  return computed(() => {
    return {
      // backgroundColor: '#00265f',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        align: 'right',
        right: 10,
        textStyle: {
          color: themeVars.value.textColor1, // 使用主题文本颜色
        },
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 35,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: ['喀什市', '疏附县', '疏勒县', '英吉沙县', '泽普县', '岳普湖县', '巴楚县', '伽师县', '叶城县', '莎车县'],
          axisLine: {
            show: true,
            lineStyle: {
              color: '#063374',
              width: 1,
              type: 'solid',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: themeVars.value.textColor2, // 使用主题文本颜色
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: '{value} kwh',
            color: themeVars.value.textColor2, // 使用主题文本颜色
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#00c7ff',
              width: 1,
              type: 'solid',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#063374',
              type: 'dashed', // 设置为虚线
            },
          },
        },
      ],
      series: [
        {
          name: '充电量',
          type: 'bar',
          data: [20, 50, 80, 58, 83, 68, 57, 80, 42, 66],
          barWidth: 8,
          barGap: 1,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2474F8' },
                { offset: 1, color: '#D7EAFE' },
              ]),
              opacity: 1,
            },
          },
        },
        {
          name: '放电量',
          type: 'bar',
          data: [50, 70, 60, 61, 75, 87, 60, 62, 86, 46],
          barWidth: 8,
          barGap: 1,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#35B543' },
                { offset: 1, color: '#D5EFD0' },
              ]),
              opacity: 1,
            },
          },
        },
      ],
    } as ECOption
  })
}

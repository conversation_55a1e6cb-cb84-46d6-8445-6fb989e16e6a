import type { FunctionalComponent } from 'vue'
import type { ButtonProps } from 'naive-ui'
import { NButton } from 'naive-ui'
import Add from '~icons/icon-park-outline/plus'
import Delete from '~icons/icon-park-outline/delete'
import Download from '~icons/icon-park-outline/download'
import Upload from '~icons/icon-park-outline/upload'
// 创建按钮
export const CreateButton: FunctionalComponent<ButtonProps> = (props, ctx) => (
  <NButton type="primary" {...props}>
    {{
      icon: () => ctx.slots.icon ? ctx.slots.icon() : <Add />,
      default: () => ctx.slots.default ? ctx.slots.default() : '新增',
    }}
  </NButton>
)

// 批量删除按钮
export const BatchDeleteButton: FunctionalComponent<ButtonProps> = (props, ctx) => (
  <NButton type="error" {...props}>
    {{
      icon: () => ctx.slots.icon ? ctx.slots.icon() : <Delete />,
      default: () => ctx.slots.default ? ctx.slots.default() : '批量删除',
      ...ctx.slots,
    }}
  </NButton>
)

// 删除按钮
export const DeleteButton: FunctionalComponent<ButtonProps> = (props, ctx) => (
  <NButton type="error" size="small" text {...props}>
    {{
      default: () => ctx.slots.default ? ctx.slots.default() : '删除',
      ...ctx.slots,
    }}
  </NButton>
)

// 导出按钮
export const ExportButton: FunctionalComponent<ButtonProps> = (props, ctx) => (
  <NButton type="warning" {...props}>
    {{
      icon: () => ctx.slots.icon ? ctx.slots.icon() : <Download />,
      default: () => ctx.slots.default ? ctx.slots.default() : '导出',
    }}
  </NButton>
)

// 导入按钮
export const ImportButton: FunctionalComponent<ButtonProps> = (props, ctx) => (
  <NButton type="primary" text {...props}>
    {{
      icon: () => ctx.slots.icon ? ctx.slots.icon() : <Upload />,
      default: () => ctx.slots.default ? ctx.slots.default() : '导入',
    }}
  </NButton>
)

// 编辑按钮
export const EditButton: FunctionalComponent<ButtonProps> = (props, ctx) => (
  <NButton type="info" size="small" text {...props}>
    {{
      default: () => ctx.slots.default ? ctx.slots.default() : '编辑',
      ...ctx.slots,
    }}
  </NButton>
)

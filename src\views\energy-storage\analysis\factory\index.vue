<script setup lang="ts">
import { EnergyStorageBackground } from '@/components/big-screen/BThemeFilter'
</script>

<template>
  <EnergyStorageBackground class="h-full">
    <BHeader title="分布式储能运营云平台" />
    <main class=" h-[calc(100%-80px)] grid grid-cols-2 grid-rows-[auto_1fr_1fr] gap-3 p-3">
      <div class="flex gap-3 col-span-2">
        <n-select class="w-300px" />
        <n-date-picker type="daterange" class="ml-auto" />
        <n-button type="primary">
          查询
        </n-button>
      </div>

      <BCard title="负荷" class="col-span-2">
        1
      </BCard>
      <BCard title="用电量">
        2
      </BCard>
      <BCard title="需量">
        3
      </BCard>
    </main>
  </EnergyStorageBackground>
</template>

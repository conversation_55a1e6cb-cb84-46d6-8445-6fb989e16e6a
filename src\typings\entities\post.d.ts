/// <reference path="../global.d.ts"/>

/** 系统岗位相关实体定义 */
namespace Entity {
  /** 岗位数据库表字段 */
  interface Post {
    /** 创建者 */
    createBy?: any
    /** 创建时间 */
    createTime?: string
    /** 更新者 */
    updateBy?: any
    /** 更新时间 */
    updateTime?: string
    /** 备注 */
    remark?: string
    /** 岗位ID */
    postId: number
    /** 岗位编码 */
    postCode: string
    /** 岗位名称 */
    postName: string
    /** 显示顺序 */
    postSort: number
    /** 状态（0正常 1停用） */
    status: '0' | '1'
    /** 用户是否存在此岗位标识 默认不存在 */
    flag?: boolean
  }
}

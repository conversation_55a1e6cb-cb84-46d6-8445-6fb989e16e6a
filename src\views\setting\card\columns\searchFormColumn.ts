import type { ProSearchFormColumns } from 'pro-naive-ui'

export function useSearchFormColumn() {
  // 使用样例数据
  const siteOptions = ref<Array<{ label: string, value: number }>>([
    { label: '站点1', value: 1 },
    { label: '站点2', value: 2 },
    { label: '站点3', value: 3 },
    { label: '站点4', value: 4 },
    { label: '站点5', value: 5 },
  ])

  return computed<ProSearchFormColumns>(() => {
    return [
      {
        title: '所属站点',
        path: 'siteId',
        field: 'select',
        placeholder: '请选择所属站点',
        fieldProps: {
          options: siteOptions.value,
        },
      },
      {
        title: '卡号查询',
        path: 'cardNumber',
        field: 'input',
        placeholder: '请输入卡号',
      },
    ]
  })
}

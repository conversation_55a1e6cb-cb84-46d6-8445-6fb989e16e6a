<script setup lang="ts">
import { configureMap } from '@/views/energy-storage/map/components/map/configureMap'
import { useStore } from '@/views/setting/site/editor/useStore'
import type { FormInst, FormRules } from 'naive-ui'
import Amap from '@/assets/imgs/setting/asset-management/amap.png'
import { ImageUpload } from '@/components/common/Upload'
import { request } from '@/service/http'
import { useDict } from '@/hooks'

const AMap = await configureMap()
const mapRef = ref<HTMLDivElement>()
const placeSearch = ref<AMap.PlaceSearch | null>(null)
const map = ref<AMap.Map | undefined>(undefined)
const addressInput = ref('')
const store = useStore()
const formRef = ref<FormInst>()

const { options: levelOptions } = useDict('measurement_method')

// 初始化地理编码器
const geocoder = new AMap.Geocoder({
  radius: 1000, // 搜索半径（米）
  extensions: 'all', // 返回详细信息
})

onMounted(() => {
  if (!mapRef.value) {
    return
  }

  map.value = markRaw(new AMap.Map(mapRef.value, {
    zoom: 3.8,
    center: [105.34, 36.312316],
    viewMode: '3D',
  }))

  // 初始化地点搜索插件
  placeSearch.value = new AMap.PlaceSearch({
    pageSize: 5, // 单页显示结果条数
    pageIndex: 1, // 页码
    city: '全国', // 限制搜索城市
    map: map.value, // 展现结果的地图实例
    autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
  })

  // 添加地图点击事件监听器
  map.value.on('click', (event: any) => {
    store.basicInfo.siteLocationY = event.lnglat.lng
    store.basicInfo.siteLocationX = event.lnglat.lat

    // 执行逆地理编码
    geocoder.getAddress(event.lnglat, (status: string, result: any) => {
      if (status === 'complete' && result.regeocode) {
        store.basicInfo.siteLocationName = result.regeocode.formattedAddress as string
      }
    })
  })
})

// 搜索地址
function searchAddress() {
  if (!addressInput.value.trim() || !placeSearch.value)
    return

  // 执行搜索
  placeSearch.value.search(addressInput.value, (status: string, result: any) => {
    if (status === 'complete' && result.info === 'OK') {
      // 搜索成功，result为对应的搜索结果
      const pois = result.poiList.pois
      if (pois && pois.length > 0) {
        // 将地图中心移动到第一个结果
        map.value?.setCenter([pois[0].lng, pois[0].lat])
      }
    }
    else {
      // 搜索失败或没有结果
      console.error('搜索失败:', result)
    }
  })
}

async function onNextStep() {
  await formRef.value?.validate()

  store.step = 2
}

const rules: FormRules = {
  siteName: {
    required: true,
    message: '请输入站点名称',
  },
  siteId: [
    {
      trigger: 'blur',
      validator: (rule: FormItemRule, value: string) => {
        return request.Get<Api.ResponseWithData<boolean>>('/base/site/checkSiteId', { params: { siteId: value } }).then((res) => {
          if (res.data) {
            throw new Error('站点编号已存在')
          }
        })
      },
    },
    {
      required: true,
      message: '请输入站点编号',
    },
  ],
  measurementWay: {
    required: true,
    message: '请选择计量方式',
  },
  siteLocationName: {
    required: true,
    message: '请在地图中选择站点地址',
  },
}
</script>

<template>
  <div class="wh-full p-2 flex gap-2 relative">
    <NCard class="basis-[370px] border-[#2090FF]!" bordered>
      <template #header>
        <n-h3 class="text-center mb-0">
          <n-text type="info">
            站点详情
          </n-text>
        </n-h3>
      </template>
      <NForm
        ref="formRef"
        :model="store.basicInfo"
        :rules="rules"
        label-placement="left"
      >
        <NFormItem path="siteName" label="站点名称">
          <NInput v-model:value="store.basicInfo.siteName" placeholder="请输入" />
        </NFormItem>
        <NFormItem path="siteId" label="站点编号">
          <NInput v-model:value="store.basicInfo.siteId" placeholder="请输入" />
        </NFormItem>

        <NFormItem path="measurementWay" label="计量方式">
          <NSelect
            v-model:value="store.basicInfo.measurementWay" filterable placeholder="请选择"
            :options="levelOptions"
          />
        </NFormItem>
        <NFormItem path="siteLocationName" label="站点地址">
          <NInput
            v-model:value="store.basicInfo.siteLocationName"
            type="textarea"
            :autosize="{
              minRows: 1,
            }"
            placeholder="请在地图中选择地址"
            readonly
          />
        </NFormItem>
        <NFormItem path="siteCardNumber" label="站点卡号">
          <NInput v-model:value="store.basicInfo.siteCardNumber" placeholder="请输入" />
        </NFormItem>
        <NFormItem path="sitePicUrl" label="电站照片">
          <ImageUpload v-model:value="store.basicInfo.sitePicUrl">
            <NUploadDragger placeholder="请输入">
              <div style="margin-bottom: 12px">
                <NovaIcon icon="carbon:document-add" />
              </div>
              <n-text style="font-size: 16px">
                点击或者拖动文件到该区域来上传
              </n-text>
            </NUploadDragger>
          </ImageUpload>
        </NFormItem>
      </NForm>
    </NCard>

    <NInput
      v-model:value="addressInput"
      class="absolute left-[430px] top-[30px] w-[400px]! z-1"
      placeholder="地址搜索"
      @keyup.enter="searchAddress"
    >
      <template #prefix>
        <img :src="Amap" alt="icon">
      </template>
    </NInput>

    <div ref="mapRef" class="rounded border border-[#2090FF] flex-[5]  h-full" />

    <footer class="absolute bottom-20px right-20px w-full text-right">
      <NButton type="info" class="mr-4" @click="$router.back()">
        返回
      </NButton>
      <NButton type="info" @click="onNextStep()">
        确定
      </NButton>
    </footer>
  </div>
</template>

<style scoped>

</style>

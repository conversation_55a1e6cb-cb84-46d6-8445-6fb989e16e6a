<script setup lang="ts">
import BHeaderStatistics from './BHeaderStatistics.vue'
import BHeaderTime from './BHeaderTime.vue'

withDefaults(defineProps<{
  title?: string
}>(), {
  title: '大屏',
})
</script>

<template>
  <header class="h-[80px] flex justify-between px-5 items-center relative z-1">
    <BHeaderStatistics />
    <n-h2 class="my-0 font-bold" style="letter-spacing:6px">
      {{ title }}
    </n-h2>
    <BHeaderTime />
  </header>
</template>

<style scoped>

</style>

# 收益分析页面

## 功能概述

收益分析页面是一个大屏可视化页面，用于展示分布式储能运营云平台的收益数据分析和统计信息。该页面提供了全面的收益分析功能，包括日收益、月收益、年收益以及详细的充放电成本分析。

## 页面结构

### 主要组件

1. **查询表单 (QueryForm.vue)**
   - 站点选择下拉框
   - 开始时间和结束时间选择器
   - 查询按钮
   - 累计统计信息显示（历史总收益、总充电量、总放电量）

2. **日收益/月收益图表 (DailyMonthlyRevenueChart.vue)**
   - 柱状图展示充电量和放电量
   - 折线图展示收益趋势
   - 支持日收益和月收益切换
   - 使用主题变量确保颜色一致性

3. **年收益图表 (AnnualRevenueChart.vue)**
   - 柱状图展示年度充电量和放电量
   - 折线图展示年度收益趋势
   - 支持年份范围选择
   - 与日收益/月收益图表类似但数据不同

4. **放电收入分析图表 (DischargeRevenueChart.vue)**
   - 堆叠柱状图展示不同时段的放电量（尖、峰、平、谷、深谷）
   - 折线图展示对应时段的电费收入
   - 支持日、月、年时间范围切换
   - 双Y轴设计，左侧显示电量，右侧显示费用

5. **充电花费分析图表 (ChargingCostChart.vue)**
   - 堆叠柱状图展示不同时段的充电量（尖、峰、平、谷、深谷）
   - 折线图展示对应时段的电费支出
   - 支持日、月、年时间范围切换
   - 双Y轴设计，左侧显示电量，右侧显示费用

## 技术特点

- 使用 ECharts 实现图表可视化
- 采用项目统一的 `useEcharts` hook 管理图表
- 支持主题变量，确保暗色主题下的显示效果
- 响应式设计，支持窗口大小变化
- 使用 NaiveUI 组件库构建界面
- 遵循项目代码风格和组件规范
- 双Y轴设计，同时展示电量和费用数据
- 堆叠柱状图与折线图组合，提供丰富的数据展示

## 数据说明

当前使用模拟数据进行演示，包括：
- 时间序列数据（日、月、年）
- 充电量和放电量数据
- 收益和成本数据
- 分时段统计数据（尖、峰、平、谷、深谷）

## 样式特点

- 使用 BCard 组件包装各个功能模块
- 统一的蓝色主题色调
- 半透明背景和边框效果
- 响应式网格布局
- 丰富的图表颜色搭配

## 使用说明

1. 在查询表单中选择站点和时间范围
2. 点击查询按钮更新数据
3. 通过标签切换查看不同时间范围的数据
4. 鼠标悬停查看图表详细信息
5. 通过时间选择器筛选特定年份的数据
6. 查看不同时段的充放电成本和收益分析

## 图表交互

- **日收益/月收益图表**: 点击标签切换日收益和月收益视图
- **年收益图表**: 使用年份选择器筛选数据范围
- **放电收入分析**: 支持日、月、年切换，查看不同时段的放电收入
- **充电花费分析**: 支持日、月、年切换，查看不同时段的充电成本

## 数据更新

页面支持实时数据更新，每5秒自动更新累计统计数据，确保数据的实时性和准确性。

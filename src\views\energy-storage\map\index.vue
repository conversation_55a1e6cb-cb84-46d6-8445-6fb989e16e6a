<script setup lang="ts">
import BHeader from '@/components/big-screen/BHeader.vue'

import IncomeOverview from './components/IncomeOverview.vue'
import Ranking from './components/Ranking.vue'
import BasicInformation from './components/BasicInformation.vue'
import Map from './components/map/index.vue'
import ElectricityOverview from './components/ElectricityOverview.vue'
import AlarmEvent from './components/AlarmEvent.vue'
</script>

<template>
  <div class="energyStorageMap h-full relative z-0 select-none">
    <BHeader title="分布式储能运营云平台" />
    <main class="h-[calc(100%-80px)] flex relative z-1 pointer-events-none p-3 pt-0">
      <div class="flex-basis-[400px] pointer-events-auto flex flex-col gap-5">
        <IncomeOverview />
        <Ranking />
      </div>
      <main class="flex-1 pointer-events-none">
        <BasicInformation />
        <Suspense>
          <Map />
          <template #fallback>
            Loading...
          </template>
        </Suspense>
      </main>
      <div class="flex-basis-[400px] pointer-events-auto flex flex-col gap-5">
        <ElectricityOverview />
        <AlarmEvent />
      </div>
    </main>
  </div>
</template>

/// <reference path="../global.d.ts"/>

/** 操作日志数据库表字段 */
namespace Entity {
  interface OperationLog {
    /** 日志主键 */
    operId: number
    /** 模块标题 */
    title: string
    /** 业务类型（0其它 1新增 2修改 3删除） */
    businessType?: number
    /** 业务类型数组 */
    businessTypes?: any
    /** 方法名称 */
    method: string
    /** 请求方式 */
    requestMethod?: string
    /** 操作类别（0其它 1后台用户 2手机端用户） */
    operatorType?: number
    /** 操作人员 */
    operName: string
    /** 部门名称 */
    deptName?: string
    /** 请求URL */
    operUrl: string
    /** 主机地址 */
    operIp: string
    /** 操作地点 */
    operLocation: string
    /** 请求参数 */
    operParam: string
    /** 返回参数 */
    jsonResult: string
    /** 操作状态（0正常 1异常） */
    status: 0 | 1
    /** 错误消息 */
    errorMsg?: string
    /** 操作时间 */
    operTime: string
    /** 消耗时间 */
    costTime?: number
    /** 数据范围 */
    dataScope?: any
    /** 创建者 */
    createBy?: string | null
    /** 创建时间 */
    createTime?: string | null
    /** 更新者 */
    updateBy?: string | null
    /** 更新时间 */
    updateTime?: string | null
    /** 备注 */
    remark?: string | null
  }
}

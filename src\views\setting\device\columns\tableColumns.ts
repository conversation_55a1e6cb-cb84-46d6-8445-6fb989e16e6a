import type { ProDataTableColumns } from 'pro-naive-ui'

export const tableColumns: ProDataTableColumns<Entity.Device> = [
  {
    type: 'selection',
  },
  {
    type: 'index',
  },
  {
    title: '设备编号',
    key: 'deviceId',
  },
  {
    title: '设备类型',
    key: 'deviceType',
  },
  {
    title: '设备名称',
    key: 'deviceName',
  },
  {
    title: '设备型号',
    key: 'deviceModel',
  },
  {
    title: '设备单位',
    key: 'deviceUnit',
  },
  {
    title: '设备数量',
    key: 'deviceNum',
  },
  {
    title: '维护周期',
    key: 'maintenanceCycle',
  },
  {
    title: '维护信息',
    key: 'maintenanceInfo',
  },
  {
    title: '规格',
    key: 'specification',
  },
  {
    title: '生产厂家',
    key: 'producer',
  },
  {
    title: '创建时间',
    key: 'createTime',
  },
]

<script setup lang="ts">
import { SettingBackground } from '@/components/big-screen/BThemeFilter'

const route = useRoute()
</script>

<template>
  <SettingBackground class="h-full w-full setting">
    <header class="h-[60px] flex items-center justify-between px-5">
      <n-h2 class="mb-0 inline-flex items-center gap-3">
        <span class="inline-block border-2px border-[var(--primary-color)] transform-rotate-45 w-[9px] h-[9px]" />
        <NText depth="1">
          {{ route.meta.title }}
        </NText>
      </n-h2>

      <BHeaderTime />
    </header>
    <main class="h-[calc(100%-60px)] px-3 pb-3">
      <RouterView />
    </main>
  </SettingBackground>
</template>

<style scoped>

</style>

<script setup lang="ts">
import BCard from '@/components/big-screen/BCard.vue'
import { useEcharts } from '@/hooks'
import BIncomeItem from '@/components/big-screen/BIncomeItem.vue'
import { useIncomeOverviewOptions } from '../echarts-options/IncomeOverviewOptions'

import Yesterday from '@/assets/imgs/energy-storage/map/yesterday.png'
import Moon from '@/assets/imgs/energy-storage/map/moon.png'
import Cumulative from '@/assets/imgs/energy-storage/map/cumulative.png'

const echartsOptions = useIncomeOverviewOptions()

useEcharts('echarts', echartsOptions)
</script>

<template>
  <BCard title="收益概况" class="flex-[4]">
    <div ref="echarts" class="h-[180px] w-full" />

    <div class="flex justify-between">
      <BIncomeItem title="昨日收益(元）" :icon="Yesterday" value="xxx" />
      <BIncomeItem title="月收益(万元）" :icon="Moon" value="xxx" />
      <BIncomeItem title="累计收益(万元）" :icon="Cumulative" value="xxx" />
    </div>
  </BCard>
</template>

<style scoped>

</style>

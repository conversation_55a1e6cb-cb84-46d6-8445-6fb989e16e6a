import type { DataTableColumns } from 'naive-ui'
import { NButton, NSpace, NSwitch, NTag } from 'naive-ui'
import type { ProSearchFormColumns } from 'pro-naive-ui'

// 搜索表单数据类型
export interface SearchFormData {
  roleName?: string
  roleKey?: string
  status?: '0' | '1'
  createTime?: [string, string]
}

// 搜索表单列配置
export const searchColumns: ProSearchFormColumns<SearchFormData> = [
  {
    title: '角色名称',
    path: 'roleName',
    field: 'input',
    fieldProps: {
      clearable: true,
    },
  },
  {
    title: '权限字符',
    path: 'roleKey',
    field: 'input',
    fieldProps: {
      clearable: true,
    },
  },
  {
    title: '角色状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      clearable: true,
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
  },
  {
    title: '创建时间',
    path: 'createTime',
    field: 'date-time-range',
    fieldProps: {
      clearable: true,
      format: 'yyyy-MM-dd',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      defaultTime: ['00:00:00', '23:59:59'],
    },
  },
]

// 表格列配置
export function createTableColumns(options: {
  onEdit: (row: Entity.Role) => void
  onDelete: (roleId: number) => void
  onStatusChange: (roleId: number, status: '0' | '1') => Promise<void>
  onDataScope: (row: Entity.Role) => void
  onUserAssign: (row: Entity.Role) => void
}): DataTableColumns<Entity.Role> {
  const { onEdit, onDelete, onStatusChange, onDataScope, onUserAssign } = options

  return [
    {
      type: 'selection',
    },
    {
      title: '角色名称',
      key: 'roleName',
      width: 150,
    },
    {
      title: '权限字符',
      key: 'roleKey',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '显示顺序',
      key: 'roleSort',
      width: 100,
      align: 'center',
    },
    {
      title: '数据权限',
      key: 'dataScope',
      width: 220,
      align: 'center',
      render: (row) => {
        const dataScopeMap: Record<string, string> = {
          1: '全部数据权限',
          2: '自定数据权限',
          3: '本部门数据权限',
          4: '本部门及以下数据权限',
          5: '仅本人数据权限',
        }
        if (dataScopeMap[row.dataScope]) {
          return <NTag bordered={false}>{dataScopeMap[row.dataScope]}</NTag>
        }
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 180,
      align: 'center',
    },
    {
      title: '备注',
      key: 'remark',
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '角色状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: (row) => {
        return (
          <NSwitch
            value={row.status === '0'}
            onUpdateValue={(value: boolean) => onStatusChange(row.roleId, value ? '0' : '1')}
          >
            {{ checked: () => '启用', unchecked: () => '禁用' }}
          </NSwitch>
        )
      },
    },
    {
      title: '操作',
      align: 'center',
      key: 'actions',
      width: 240,
      fixed: 'right',
      render: (row) => {
        return (
          <NSpace justify="center">
            <NButton
              text
              onClick={() => onEdit(row)}
            >
              编辑
            </NButton>
            <NButton
              text
              type="info"
              onClick={() => onDataScope(row)}
            >
              数据权限
            </NButton>
            <NButton
              text
              type="warning"
              onClick={() => onUserAssign(row)}
            >
              分配用户
            </NButton>
            <NButton
              text
              type="error"
              onClick={() => onDelete(row.roleId)}
            >
              删除
            </NButton>
          </NSpace>
        )
      },
    },
  ]
}

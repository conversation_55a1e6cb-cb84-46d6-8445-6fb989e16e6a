import BusbarNode from '@/views/setting/site/editor/nodes/BusbarNode.vue'
import EnergyMeterNode from '@/views/setting/site/editor/nodes/EnergyMeterNode.vue'
import LcmNode from '@/views/setting/site/editor/nodes/LcmNode.vue'
import RingMainUnitNode from '@/views/setting/site/editor/nodes/RingMainUnitNode.vue'
import GridTieNode from '@/views/setting/site/editor/nodes/GridTieNode.vue'
import ChargingStackNode from '@/views/setting/site/editor/nodes/ChargingStackNode.vue'
import type { Raw } from 'vue'

// 和字典 device_type 中的值对应
export enum CustomNodeType {
  Busbar = 'busbar',
  EnergyMeter = 'electric_energy_meter',
  Bem = 'bem_device',
  EnergyStorage = 'energy_storage_cabinet',
  Inverter = 'inverter',
  RelayHost = 'relay_host',
  Ems = 'station_ems',
  Switch = 'network_switch',
  ChargingPile = 'charging_pile',
  ChargingStack = 'charging_heap',
  GridTie = 'grid_connection_cabinet',
  RingMainUnit = 'ring_main_unit',
  Lcm = 'lcm_module',
}

// @see https://vueflow.dev/guide/node.html#node-types-object
export const nodeTypes: Record<CustomNodeType, Raw<Component>> = {
  [CustomNodeType.Busbar]: markRaw(BusbarNode),
  [CustomNodeType.EnergyMeter]: markRaw(EnergyMeterNode),
  [CustomNodeType.Bem]: markRaw(EnergyMeterNode),
  [CustomNodeType.EnergyStorage]: markRaw(EnergyMeterNode),
  [CustomNodeType.Inverter]: markRaw(EnergyMeterNode),
  [CustomNodeType.RelayHost]: markRaw(EnergyMeterNode),
  [CustomNodeType.Ems]: markRaw(EnergyMeterNode),
  [CustomNodeType.Switch]: markRaw(EnergyMeterNode),
  [CustomNodeType.ChargingPile]: markRaw(EnergyMeterNode),
  [CustomNodeType.ChargingStack]: markRaw(ChargingStackNode),
  [CustomNodeType.GridTie]: markRaw(GridTieNode),
  [CustomNodeType.RingMainUnit]: markRaw(RingMainUnitNode),
  [CustomNodeType.Lcm]: markRaw(LcmNode),
}

<script setup lang="ts">
import { createProModalForm } from 'pro-naive-ui'
import { useStore } from '@/views/setting/site/editor/useStore'
import AssetFormContent from '@/views/setting/site/editor/components/AssetFormContent.vue'

const store = useStore()

const form = createProModalForm<Entity.Asset>({
  onSubmit(values) {
    const index = store.assets.findIndex(asset => asset.id === values.id)
    store.assets.splice(index, 1, toRaw(values))
    form.close()
  },
})

defineExpose(form)
</script>

<template>
  <ProModalForm
    title="修改资产信息"
    width="800px"
    preset="card"
    :form="form"
  >
    <AssetFormContent />
  </ProModalForm>
</template>

<script setup lang="ts">
import BasicInformationItem from '@/views/energy-storage/map/components/common/BasicInformationItem.vue'

import PowerStation from '@/assets/imgs/energy-storage/map/power-station.png'
import EnergyStorageScale from '@/assets/imgs/energy-storage/map/energy-storage-scale.png'
import AssembleInstalledPower from '@/assets/imgs/energy-storage/map/assemble-installed-power.png'
import AssembleInstalledCapacity from '@/assets/imgs/energy-storage/map/assemble-installed-capacity.png'
</script>

<template>
  <div class="basicInformation h-[60px] w-full flex justify-around px-5 items-center pointer-events-auto">
    <BasicInformationItem :icon="PowerStation" name="累计电站总数" value="123" unit="个" />
    <BasicInformationItem :icon="EnergyStorageScale" name="储能规模" value="123" unit="MWh" />
    <BasicInformationItem :icon="AssembleInstalledPower" name="总装机功率" value="123" unit="KW" />
    <BasicInformationItem :icon="AssembleInstalledCapacity" name="总装机容量" value="123" unit="MWh" />
  </div>
</template>

<style scoped>

</style>

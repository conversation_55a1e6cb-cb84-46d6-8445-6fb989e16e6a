import { useThemeVars } from 'naive-ui'
import type { ECOption } from '@/hooks'

export function usePowerOptions() {
  const themeVars = useThemeVars()

  return computed<ECOption>(() => {
    return {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        textStyle: {
          color: themeVars.value.textColor1, // 使用主题文字颜色
        },
      },
      grid: {
        left: '3%',
        right: '1%',
        bottom: '65',
        top: '30',
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      xAxis: {
        type: 'time',
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: themeVars.value.textColor2, // Y轴字体颜色（次级）
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: themeVars.value.textColor3,
          },
        },
      },
      dataZoom: [
        {
          show: true,
          start: 0,
          end: 100,
        },
      ],
      series: [
        {
          type: 'line',
          markArea: {
            label: {
              color: themeVars.value.textColor2,
              position: 'insideTop',
            },
            data: [
              [
                {
                  name: '尖',
                  xAxis: new Date('2025-07-24 01:00').getTime(),
                  itemStyle: {
                    color: '#B8DDFD44',
                  },
                },
                {
                  xAxis: new Date('2025-07-24 02:30').getTime(),
                },
              ],
              [
                {
                  name: '峰',
                  xAxis: new Date('2025-07-24 02:30').getTime(),
                  itemStyle: {
                    color: 'transparent',
                  },
                },
                {
                  xAxis: new Date('2025-07-24 04:30').getTime(),
                },
              ],
              [
                {
                  name: '平',
                  xAxis: new Date('2025-07-24 04:30').getTime(),
                  itemStyle: {
                    color: '#B8DDFD44',
                  },
                },
                {
                  xAxis: new Date('2025-07-24 06:30').getTime(),
                },
              ],
              [
                {
                  name: '谷',
                  xAxis: new Date('2025-07-24 06:30').getTime(),
                  itemStyle: {
                    color: 'transparent',
                  },
                },
                {
                  xAxis: new Date('2025-07-24 08:30').getTime(),
                },
              ],
              [
                {
                  name: '尖',
                  xAxis: new Date('2025-07-24 08:30').getTime(),
                  itemStyle: {
                    color: '#B8DDFD44',
                  },
                },
                {
                  xAxis: new Date('2025-07-24 10:30').getTime(),
                },
              ],
            ],
          },
        },
        {
          name: '用电量',
          type: 'line',
          smooth: true,
          data: [
            [new Date('2025-07-24 00:00:00').getTime(), 300],
            [new Date('2025-07-24 02:24:00').getTime(), 450],
            [new Date('2025-07-24 04:48:00').getTime(), 620],
            [new Date('2025-07-24 07:12:00').getTime(), 780],
            [new Date('2025-07-24 09:36:00').getTime(), 920],
            [new Date('2025-07-24 12:00:00').getTime(), 850],
          ],
        },
        {
          name: '储能功率',
          type: 'line',
          stack: 'Total',
          data: [
            [new Date('2025-07-24 00:00:00').getTime(), 320],
            [new Date('2025-07-24 02:24:00').getTime(), 410],
            [new Date('2025-07-24 04:48:00').getTime(), 580],
            [new Date('2025-07-24 07:12:00').getTime(), 720],
            [new Date('2025-07-24 09:36:00').getTime(), 890],
            [new Date('2025-07-24 12:00:00').getTime(), 760],
          ],
          lineStyle: { width: 2 },
          itemStyle: { color: '#91CC75' }, // 绿色
        },
        {
          name: '负荷功率',
          type: 'line',
          stack: 'Total',
          data: [
            [new Date('2025-07-24 00:00:00').getTime(), 280],
            [new Date('2025-07-24 02:24:00').getTime(), 350],
            [new Date('2025-07-24 04:48:00').getTime(), 500],
            [new Date('2025-07-24 07:12:00').getTime(), 950], // 早高峰
            [new Date('2025-07-24 09:36:00').getTime(), 820],
            [new Date('2025-07-24 12:00:00').getTime(), 680],
          ],
          lineStyle: { width: 2 },
          itemStyle: { color: '#EE6666' }, // 红色
        },
        {
          name: '策略功率',
          type: 'line',
          stack: 'Total',
          data: [
            [new Date('2025-07-24 00:00:00').getTime(), 150],
            [new Date('2025-07-24 02:24:00').getTime(), 120],
            [new Date('2025-07-24 04:48:00').getTime(), 300],
            [new Date('2025-07-24 07:12:00').getTime(), 650],
            [new Date('2025-07-24 09:36:00').getTime(), 880],
            [new Date('2025-07-24 12:00:00').getTime(), 930],
          ],
          lineStyle: { width: 2 },
          itemStyle: { color: '#FAC858' }, // 黄色
        },
        {
          name: '需量限制',
          type: 'line',
          stack: 'Total',
          data: [
            [new Date('2025-07-24 00:00:00').getTime(), 420],
            [new Date('2025-07-24 02:24:00').getTime(), 180], // 突降
            [new Date('2025-07-24 04:48:00').getTime(), 750], // 突增
            [new Date('2025-07-24 07:12:00').getTime(), 620],
            [new Date('2025-07-24 09:36:00').getTime(), 380],
            [new Date('2025-07-24 12:00:00').getTime(), 910],
          ],
          lineStyle: { width: 2 },
          itemStyle: { color: '#73C0DE' }, // 浅蓝
        },
        {
          name: '逆功率限制',
          type: 'line',
          stack: 'Total',
          data: [
            [new Date('2025-07-24 00:00:00').getTime(), 200],
            [new Date('2025-07-24 02:24:00').getTime(), 350],
            [new Date('2025-07-24 04:48:00').getTime(), 500],
            [new Date('2025-07-24 07:12:00').getTime(), 650],
            [new Date('2025-07-24 09:36:00').getTime(), 800],
            [new Date('2025-07-24 12:00:00').getTime(), 950],
          ],
          lineStyle: { width: 2 },
          itemStyle: { color: '#3BA272' }, // 深绿
        },
        {
          name: 'SOC',
          type: 'line',
          stack: 'Total',
          data: [
            [new Date('2025-07-24 00:00:00').getTime(), 250], // 深夜低电量
            [new Date('2025-07-24 02:24:00').getTime(), 180], // 凌晨最低
            [new Date('2025-07-24 04:48:00').getTime(), 420], // 早间开始上升
            [new Date('2025-07-24 07:12:00').getTime(), 880], // 早高峰（上班时段）
            [new Date('2025-07-24 09:36:00').getTime(), 650], // 上午回落
            [new Date('2025-07-24 12:00:00').getTime(), 720], // 午间小高峰
          ],
          lineStyle: { width: 2 },
          itemStyle: { color: '#FC8452' }, // 橙色
        },
      ],
    }
  })
}
